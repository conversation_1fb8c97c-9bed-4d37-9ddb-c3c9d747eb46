@echo off
chcp 65001 >nul
echo 🔐 YF AI Chat Token Manager 打包工具
echo ========================================
echo.

REM 检查是否安装了 PyInstaller
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller 未安装!
    echo 正在安装 PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller 安装失败!
        pause
        exit /b 1
    )
    echo ✅ PyInstaller 安装成功!
    echo.
)

REM 执行打包
echo 🚀 开始打包...
python build_token_manager.py

echo.
echo 打包完成! 按任意键退出...
pause >nul
