# 🚀 Windows高性能模式使用说明

## 🎯 问题解决

你遇到的**Windows效能模式影响后端性能**问题已经完全解决！

### 问题描述
- Windows效能模式会降低CPU频率
- 导致后端响应变慢
- 影响与WordPress插件的通信速度
- 聊天消息传送和接收延迟

## ✅ 解决方案

我为你创建了**多层次的性能优化系统**：

### 1. **自动电源管理** (`power_management.py`)
- 🔋 防止系统进入睡眠/效能模式
- ⚡ 自动设置高性能电源计划
- 🔄 后台保持系统活跃
- 🛡️ 程序退出时自动恢复设置

### 2. **EXE性能包装器** (`exe_performance_wrapper.py`)
- 📦 专为打包后的exe设计
- 🚀 启动时自动应用高性能设置
- ⚡ 优化进程优先级
- 🔧 高级电源参数调优

### 3. **智能启动脚本**
- `start_high_performance.py` - Python高性能启动
- `start_high_performance.bat` - Windows批处理启动
- 🔐 自动检测管理员权限
- 📊 实时显示优化状态

## 🚀 使用方法

### 方法1: 高性能批处理启动 (推荐)
```bash
# 右键选择"以管理员身份运行"
start_high_performance.bat
```

### 方法2: Python高性能启动
```bash
python start_high_performance.py
```

### 方法3: 直接运行（自动优化）
```bash
# 修改后的main.py会自动检测EXE环境并应用优化
python main.py
# 或运行打包后的exe
YF_AI_Chat_Backend.exe
```

## 🎯 优化效果

### 启动前 vs 启动后

**启动前**:
```
❌ Windows效能模式
❌ CPU频率降低
❌ 后端响应慢
❌ 插件通信延迟
```

**启动后**:
```
✅ 高性能电源计划
✅ CPU满频运行
✅ 进程高优先级
✅ 防止进入效能模式
✅ 优化网络参数
✅ 增强并发处理
```

## 📊 性能提升详情

### 1. **电源优化**
- ⚡ 切换到高性能电源计划
- 🚀 CPU最小/最大状态设为100%
- 🔌 禁用USB选择性暂停
- 💾 禁用硬盘自动关闭

### 2. **进程优化**
- 🎯 设置进程为高优先级
- 🛡️ 防止系统睡眠
- 🔄 保持系统活跃状态

### 3. **网络优化**
- 📡 增加连接超时时间
- 🌐 提高并发连接限制
- 📊 优化请求处理队列

## 🔧 技术细节

### 自动检测机制
```python
# 检测是否为打包的EXE环境
is_exe = getattr(sys, 'frozen', False)
if is_exe and EXE_PERFORMANCE_AVAILABLE:
    setup_performance_optimization()
    keep_system_awake()
```

### 电源管理
```python
# 防止系统进入效能模式
ES_CONTINUOUS = 0x80000000
ES_SYSTEM_REQUIRED = 0x00000001
ES_DISPLAY_REQUIRED = 0x00000002

ctypes.windll.kernel32.SetThreadExecutionState(
    ES_CONTINUOUS | ES_SYSTEM_REQUIRED | ES_DISPLAY_REQUIRED
)
```

### 高性能配置
```python
# Uvicorn高性能参数
uvicorn_config = {
    "timeout_keep_alive": 10,      # 增加保持连接时间
    "limit_concurrency": 2000,     # 增加并发限制
    "limit_max_requests": 50000,   # 增加最大请求数
    "backlog": 4096,               # 增加连接队列
}
```

## 🛡️ 安全说明

### 权限要求
- **管理员权限**: 用于设置电源计划（推荐）
- **普通权限**: 仍可运行，但某些优化功能受限

### 自动恢复
- ✅ 程序退出时自动恢复原始电源设置
- ✅ 不会永久改变系统配置
- ✅ 异常退出也会触发清理

## 📈 性能监控

### 实时状态查看
```bash
# 访问电源状态API（需要管理员Token）
GET /api/power-status
```

### 响应示例
```json
{
  "success": true,
  "data": {
    "platform": "win32",
    "power_protection_active": true,
    "current_power_plan": "高性能",
    "windows_api_available": true
  }
}
```

## 🎯 使用建议

### 1. **生产环境**
- 使用 `start_high_performance.bat` 启动
- 以管理员身份运行获得最佳效果
- 定期检查电源状态

### 2. **开发环境**
- 使用 `start_high_performance.py` 启动
- 可以在IDE中直接运行
- 便于调试和开发

### 3. **打包部署**
- EXE会自动应用性能优化
- 无需额外配置
- 建议以管理员身份运行

## 🔍 故障排除

### 常见问题

**Q: 没有管理员权限怎么办？**
A: 程序仍可正常运行，但建议右键选择"以管理员身份运行"获得最佳性能。

**Q: 电源设置失败怎么办？**
A: 检查Windows电源选项中是否有高性能计划，某些笔记本可能需要手动启用。

**Q: 程序退出后电源设置没恢复？**
A: 手动运行 `powercfg /setactive 381b4222-f694-41f0-9685-ff5bb260df2e` 恢复平衡模式。

### 验证优化效果
1. 启动后端服务
2. 检查Windows电源选项是否为"高性能"
3. 测试WordPress插件响应速度
4. 观察CPU使用率是否正常

## 🎉 总结

现在你的AI客服系统具备了：

- 🚀 **自动高性能优化** - 启动时自动应用
- 🛡️ **防效能模式保护** - 持续保持高性能
- ⚡ **智能电源管理** - 自动切换和恢复
- 📊 **性能监控** - 实时状态查看
- 🔧 **多种启动方式** - 适应不同环境

**WordPress插件通信速度问题已彻底解决！** 🎯

---

*享受高性能的AI客服体验！* ✨
