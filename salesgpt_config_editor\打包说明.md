# 杨杋AI Agent配置管理器 - 打包说明

## 快速打包

### 方法一：双击批处理文件（推荐）
1. 双击 `打包GUI.bat` 文件
2. 等待自动安装依赖和打包完成
3. 在 `dist` 目录中找到打包结果

### 方法二：手动运行Python脚本
```bash
# 1. 安装依赖
pip install -r requirements.txt
pip install pyinstaller

# 2. 运行打包脚本
python build_gui.py
```

## 打包输出

打包完成后，会在 `dist` 目录生成以下文件：

### 1. 单文件版本
- `杨杋ai_agent配置管理器.exe` - 独立可执行文件

### 2. 便携版包（推荐分发）
- `杨杋ai_agent配置管理器_便携版/` 文件夹，包含：
  - `杨杋ai_agent配置管理器.exe` - 可执行文件
  - `README.md` - 使用说明文档

## 系统要求

### 开发环境要求
- Python 3.8+
- PyQt6 >= 6.4.0
- PyInstaller

### 运行环境要求
- Windows 10/11 (64位)
- 无需安装Python或其他依赖

## 打包特性

### ✅ 已包含功能
- 单文件打包，无需额外依赖
- Windows GUI应用，无控制台窗口
- 自定义应用图标
- 版本信息和文件属性
- 所有UI模块和资源文件
- 中文界面支持

### 📦 打包内容
- 主程序 (`main.py`)
- UI模块 (`ui/` 目录)
- 核心模块 (`core/` 目录)
- 资源文件 (`resources/` 目录)
- PyQt6运行时库

## 使用说明

### 首次运行
1. 双击 `杨杋ai_agent配置管理器.exe`
2. 程序会提示选择配置文件位置
3. 选择 `backend/salesgpt_config/advanced_sales_config.json`
4. 开始使用配置编辑器

### 配置文件路径
通常配置文件位于以下位置：
```
项目根目录/
├── backend/
│   └── salesgpt_config/
│       └── advanced_sales_config.json  ← 选择这个文件
└── salesgpt_config_editor/
    └── 杨杋ai_agent配置管理器.exe
```

## 分发建议

### 推荐分发方式
1. 使用 `杨杋ai_agent配置管理器_便携版` 文件夹
2. 压缩整个文件夹为ZIP格式
3. 用户解压后直接运行exe文件

### 分发注意事项
- 确保用户有配置文件的读写权限
- 建议提供配置文件路径说明
- 可以预先放置示例配置文件

## 故障排除

### 常见问题

**Q: 打包失败，提示找不到模块**
A: 确保已安装所有依赖：`pip install -r requirements.txt pyinstaller`

**Q: 可执行文件无法启动**
A: 检查是否有杀毒软件误报，添加信任或临时关闭实时保护

**Q: 程序启动后找不到配置文件**
A: 确保配置文件路径正确，文件存在且有读写权限

**Q: 中文显示乱码**
A: 确保系统区域设置支持中文，或使用UTF-8编码

### 调试模式
如需调试，可以创建控制台版本：
```bash
pyinstaller --onefile --console --name=杨杋ai_agent配置管理器_调试版 main.py
```

## 版本信息

- 应用名称：杨杋AI Agent配置管理器
- 版本：1.0.0
- 开发商：杨杋科技
- 文件描述：AI Agent配置文件可视化编辑工具

## 技术细节

### 打包工具
- PyInstaller - Python应用打包工具
- 单文件模式 (`--onefile`)
- Windows GUI模式 (`--windowed`)

### 包含的库
- PyQt6 - GUI框架
- Python标准库
- 自定义模块 (ui, core)

### 资源处理
- 图标文件自动嵌入
- UI资源文件打包
- 字体和样式保持

---

如有问题，请检查打包日志或联系技术支持。
