# 🔐 Token系统工作原理详解

## 📋 概述

YF AI Chat 使用多Token架构来实现不同客户端的权限控制。Token生成器和后端系统通过环境变量配置文件进行连接。

## 🔄 Token生成器与后端的关系

### 1. **Token生成器的作用**
```
YF_AI_Token_Manager.exe
    ↓ 生成三种token
    ↓ 保存到 generated_tokens.json
    ↓ 管理员手动复制到 .env 文件
    ↓ 后端读取 .env 文件中的token
```

### 2. **后端如何识别Token类型**

后端通过以下方式识别token：

#### A. 环境变量配置 (backend/.env)
```env
# 三种不同类型的token
WORDPRESS_TOKEN=aP1mvhyVfcj46ROROq9LtE3TtfPZPXjl
ADMIN_TOKEN=PZ90yBE5ia5peWcMsq6fWZWeO94JZmit
API_TOKEN=XsZWpOE6KhAXY3pHpNfW2vSzuBIPwpUK
```

#### B. 后端Token映射 (main.py)
```python
# Token管理 - 支持多种类型的token
TOKENS = {
    "wordpress": os.getenv("WORDPRESS_TOKEN", "wp_default_token"),
    "admin": os.getenv("ADMIN_TOKEN", "admin_default_token"),
    "api": os.getenv("API_TOKEN", "api_default_token")
}

# Token权限映射
TOKEN_PERMISSIONS = {
    "wordpress": ["chat"],  # 只能访问聊天API
    "admin": ["chat", "admin", "websocket"],  # 可以访问所有功能
    "api": ["chat", "admin", "websocket"]  # 完整权限（向后兼容）
}
```

## 🔍 Token验证流程

### 1. **客户端发送请求**
```http
POST /api/chat
Authorization: Bearer aP1mvhyVfcj46ROROq9LtE3TtfPZPXjl
Content-Type: application/json
```

### 2. **后端验证过程**
```python
def verify_token(required_permission: str = "chat"):
    def _verify_token(credentials):
        token = credentials.credentials  # 获取token
        
        # 步骤1: 查找token类型
        token_type = None
        for t_type, t_value in TOKENS.items():
            if token == t_value:  # 比较token值
                token_type = t_type   # 确定类型
                break
        
        # 步骤2: 验证token有效性
        if not token_type:
            raise HTTPException(401, "Invalid token")
        
        # 步骤3: 检查权限
        if required_permission not in TOKEN_PERMISSIONS.get(token_type, []):
            raise HTTPException(403, "Insufficient permissions")
        
        return {"token": token, "type": token_type}
```

## 🎯 三种Token的具体用途

### 1. **WORDPRESS_TOKEN**
- **用途**: WordPress插件专用
- **权限**: 只能访问 `/api/chat` 端点
- **使用场景**: 网站访客通过WordPress插件发送聊天消息
- **安全级别**: 最低（只能聊天）

### 2. **ADMIN_TOKEN**
- **用途**: 客服端专用
- **权限**: 可以访问所有管理功能和WebSocket连接
- **使用场景**: 客服人员使用客服端管理聊天会话
- **安全级别**: 最高（完整管理权限）

### 3. **API_TOKEN**
- **用途**: 通用API访问
- **权限**: 完整权限（向后兼容）
- **使用场景**: 第三方系统集成或开发测试
- **安全级别**: 高（完整权限）

## 🔗 Token如何知道是"一组"的

### 问题解答：
**后端并不知道三个token是"一组"的！**

### 实际工作原理：
1. **独立验证**: 每个token都是独立验证的
2. **类型识别**: 后端通过比较token值来确定类型
3. **权限控制**: 根据token类型分配相应权限

### 示例说明：
```python
# 假设生成的token组：
generated_tokens = {
    "WORDPRESS_TOKEN": "abc123",
    "ADMIN_TOKEN": "def456", 
    "API_TOKEN": "ghi789"
}

# 后端配置：
TOKENS = {
    "wordpress": "abc123",  # 从环境变量读取
    "admin": "def456",      # 从环境变量读取
    "api": "ghi789"         # 从环境变量读取
}

# 当客户端发送 "abc123" 时：
# 1. 后端遍历TOKENS字典
# 2. 发现 "abc123" == TOKENS["wordpress"]
# 3. 确定token_type = "wordpress"
# 4. 检查权限：TOKEN_PERMISSIONS["wordpress"] = ["chat"]
```

## 📝 配置更新流程

### 手动配置流程：
1. **生成Token**: 运行 `YF_AI_Token_Manager.exe`
2. **查看结果**: 打开 `generated_tokens.json`
3. **复制Token**: 手动复制三个token值
4. **更新配置**: 编辑 `backend/.env` 文件
5. **重启后端**: 重启后端服务使配置生效

### 配置示例：
```env
# 更新前
WORDPRESS_TOKEN=old_wp_token
ADMIN_TOKEN=old_admin_token
API_TOKEN=old_api_token

# 更新后（从generated_tokens.json复制）
WORDPRESS_TOKEN=aP1mvhyVfcj46ROROq9LtE3TtfPZPXjl
ADMIN_TOKEN=PZ90yBE5ia5peWcMsq6fWZWeO94JZmit
API_TOKEN=XsZWpOE6KhAXY3pHpNfW2vSzuBIPwpUK
```

## ⚠️ 重要注意事项

1. **Token生成器不会自动更新后端配置**
2. **需要手动复制token到.env文件**
3. **更新配置后需要重启后端服务**
4. **每个token都是独立的，没有"组"的概念**
5. **后端通过token值匹配来确定类型和权限**

## 🔧 自动化建议

如果需要自动化配置更新，可以考虑：
1. 修改token生成器直接更新.env文件
2. 创建配置同步脚本
3. 使用配置管理工具

---

**总结**: Token生成器只负责生成安全的随机token，后端通过环境变量配置和token值比较来实现类型识别和权限控制。
