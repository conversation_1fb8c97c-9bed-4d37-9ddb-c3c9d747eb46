@echo off
chcp 65001 >nul
title YF AI Chat Backend - 改进版

echo.
echo ========================================
echo   YF AI Chat Backend - 改进版启动
echo ========================================
echo.

:: 检查虚拟环境
if not exist ".venv\Scripts\activate.bat" (
    echo ❌ 虚拟环境不存在，请先创建虚拟环境：
    echo    python -m venv .venv
    echo    .venv\Scripts\activate
    echo    pip install -r requirements.txt
    pause
    exit /b 1
)

:: 激活虚拟环境
echo 📦 激活虚拟环境...
call .venv\Scripts\activate.bat

:: 检查依赖
echo 🔍 检查依赖...
python -c "import fastapi, uvicorn, langchain_google_genai" 2>nul
if errorlevel 1 (
    echo ❌ 依赖缺失，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

:: 启动改进版服务
echo.
echo 🚀 启动改进版后端服务...
echo 💡 此版本已优化连接错误处理
echo.
python start_improved.py

echo.
echo 👋 服务已停止
pause
