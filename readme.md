# YF AI Chat Now - 智能客服系统

一个完整的AI客服解决方案，包含WordPress插件、后端API服务和PyQt6客服管理端。

## 🚀 核心特性

- **WordPress插件**: 网站集成聊天功能，支持多位置悬浮按钮
- **后端API服务**: FastAPI + SQLite，支持Gemini/OpenAI/DeepSeek多LLM
- **客服管理端**: PyQt6桌面应用，实时监控和人工接管
- **多Token安全**: 分级权限管理，保障系统安全
- **代理支持**: SOCKS5代理，支持全球访问
- **实时通信**: WebSocket实时消息推送
- **API防护**: 多层防护机制，防止滥用和攻击
- **SalesGPT智能销售**: Dylan AI销售助手，专业的销售流程管理和产品咨询
- **阶段化销售管理**: 自动跟踪销售阶段，从介绍到成交的完整流程
- **智能客服转接**: 自动识别复杂问题，无缝转接人工客服
- **长期偏好记忆**: 智能学习用户偏好，提供个性化服务体验

## 📁 项目结构

```
yf-ai-chat/
├── backend/                 # 🚀 后端API服务
│   ├── main.py             # FastAPI主应用 (启动入口)
│   ├── chat_data.db        # SQLite数据库
│   ├── models/             # 数据模型
│   ├── services/           # 服务层
│   │   ├── salesgpt_service.py  # SalesGPT智能销售服务
│   │   ├── database.py     # 数据库服务
│   │   └── customer_service.py  # 客服管理服务
│   ├── agent_config/       # 传统Agent配置 (已弃用)
│   ├── salesgpt_config/    # 🤖 SalesGPT配置
│   │   ├── product_catalog.txt  # 产品目录
│   │   └── README.md       # 配置说明
│   ├── config/             # 防护配置
│   ├── utils/              # 工具类
│   ├── clear_database.py   # 数据库清理工具
│   ├── token_manager.py    # Token管理工具
│   └── requirements.txt    # Python依赖 (包含LangChain)
├── client/                 # 💻 PyQt6客服端
│   ├── main.py             # 客服端主应用 (启动入口)
│   ├── user_manager.py     # 用户管理
│   ├── users.json          # 用户数据
│   ├── config_helper.py    # 配置助手
│   ├── quick_config.py     # 快速配置
│   ├── config.bat          # Windows配置脚本
│   ├── utils/              # 工具类
│   └── requirements.txt    # Python依赖
├── yf ai chat now/         # 🔌 WordPress插件
│   ├── yf-ai-chat-now.php  # 主插件文件
│   ├── assets/             # 前端资源
│   └── templates/          # 模板文件
├── tests/                  # 🧪 测试文件
│   ├── test_*.py           # 各种功能测试
│   └── README.md           # 测试说明
├── docs/                   # 📚 项目文档
│   ├── *_SUMMARY.md        # 功能开发总结
│   └── README.md           # 文档说明
├── backups/                # 💾 备份文件
│   ├── *.backup            # 数据库备份
│   └── README.md           # 备份说明
├── blacklist_cache/        # 🛡️ IP黑名单缓存
└── README.md               # 项目文档
```

## ⚙️ 配置说明

### 后端配置 (.env)
```env
# LLM配置 (gemini/openai/deepseek)
LLM_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-2.0-flash-lite
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo

# DeepSeek配置
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 代理配置
PROXY_ENABLED=true          # 代理开关 (true/false)
PROXY_TYPE=socks5           # 代理类型
PROXY_HOST=**************   # 代理主机
PROXY_PORT=33489            # 代理端口

# 服务器配置
HOST=0.0.0.0
PORT=8000

# Token配置 - 不同类型的访问令牌
WORDPRESS_TOKEN=wp_secure_token_here
ADMIN_TOKEN=admin_secure_token_here
API_TOKEN=api_secure_token_here

# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///backend/chat_data.db

# 时区配置
TIMEZONE=America/Los_Angeles

# 管理员超时设置 (分钟)
ADMIN_TIMEOUT_MINUTES=15

# SalesGPT配置
SALESPERSON_NAME=Dylan
COMPANY_NAME=KNKA Environmental Appliances
COMPANY_WEBSITE=knkalife.com

# API防护配置
PROTECTION_ENABLED=true
IP_BLACKLIST_ENABLED=true
RATE_LIMIT_ENABLED=true
```

### 客服端配置 (.env)
```env
# 服务器配置 - 支持本地和远程连接
SERVER_HOST=localhost
SERVER_PORT=8000
ADMIN_TOKEN=admin_secure_token_here

# SSL/HTTPS配置
# 本地局域网连接建议设置为false，远程连接建议设置为true
USE_HTTPS=false

# 默认管理员账号
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin

# 时区配置
TIMEZONE=America/Los_Angeles

# 配置示例:
# 本地开发: SERVER_HOST=localhost, USE_HTTPS=false
# 局域网连接: SERVER_HOST=*************, USE_HTTPS=false
# 远程连接: SERVER_HOST=chat.22668.xyz, USE_HTTPS=true
```

### 🔧 客服端快速配置

客服端提供了便捷的配置工具：

```bash
cd client

# Windows用户 - 图形化配置
config.bat

# 命令行快速配置
python quick_config.py local              # 本地环境
python quick_config.py lan *************  # 局域网环境
python quick_config.py remote             # 远程环境

# 交互式配置
python config_helper.py
```

### 🔐 Token管理

YF AI Chat使用多Token架构，为不同的客户端提供不同级别的访问权限，提高系统安全性。

**Token类型说明**:
- **WORDPRESS_TOKEN**: WordPress插件专用，只能访问聊天API (`/api/chat`)
- **ADMIN_TOKEN**: 客服端专用，可访问所有管理功能和WebSocket连接
- **API_TOKEN**: 通用API访问，完整权限（向后兼容）

**生成安全Token**:
```bash
cd backend
python token_manager.py
```

这个工具会：
1. 生成32位安全随机Token
2. 自动更新 `backend/.env` 文件
3. 自动更新 `client/.env` 文件
4. 显示Token配置摘要

**安全最佳实践**:
- ❌ 不要在代码中硬编码Token
- ❌ 不要将Token提交到版本控制系统
- ❌ 不要在日志中记录完整Token
- ✅ 使用环境变量存储Token
- ✅ 限制Token的访问权限
- ✅ 定期检查Token使用情况
- ✅ 建议每3-6个月更换一次Token

## 🛡️ API防护机制

系统提供多层防护机制，保护API免受滥用和攻击：

### 防护功能
- **频率限制**: 限制单个IP的请求频率（默认每分钟10次）
- **内容过滤**: 检测垃圾消息、重复字符、特殊字符滥用
- **消息长度限制**: 防止超长消息消耗过多API资源（默认1000字符）
- **IP黑名单**: 自动封禁违规IP，支持手动管理
- **智能IP检测**: 支持CDN和代理环境的真实IP获取

### 配置建议
**电商客服场景**:
- 频率限制：每分钟8次
- 消息长度：800字符
- 自动封禁：5分钟内违规3次

**技术支持场景**:
- 频率限制：每分钟12次
- 消息长度：1500字符
- 自动封禁：5分钟内违规5次

### 管理界面
WordPress后台 → 工具 → YF AI Chat 安全
- 实时统计封禁IP数量和安全事件
- IP黑名单管理（手动封禁/解封）
- 安全日志查看和分析
- 封禁记录历史查询

## 🔧 快速开始

⚠️ **重要提示**: 所有Python组件都必须在虚拟环境中运行，推荐使用 `.venv` 作为虚拟环境名称。

### 1. 后端服务
```bash
cd backend

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置

# 启动服务
python main.py
```

### 2. WordPress插件
1. 上传 `yf ai chat now` 文件夹到 `wp-content/plugins/`
2. 在WordPress后台激活插件
3. 配置API Token和服务器地址

### 3. 客服端
```bash
cd client

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置

# 启动客服端
python main.py
```

## 🌐 API接口文档

### 基础信息
- **基础URL**: `https://chat.22668.xyz:8000` (生产环境) / `http://localhost:8000` (本地开发)
- **认证方式**: Bearer Token
- **数据格式**: JSON

### 主要端点
- `POST /api/chat` - 发送聊天消息
- `GET /api/admin/sessions` - 获取会话列表
- `GET /api/admin/session/{session_id}/messages` - 获取会话消息
- `POST /api/admin/message` - 管理员发送消息
- `WS /ws/admin/{admin_id}` - 管理员WebSocket连接

### 认证Header
```
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json
```

### 聊天API示例
```javascript
// WordPress插件发送消息
fetch('/wp-admin/admin-ajax.php', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: new URLSearchParams({
    action: 'yf_chat_message',
    message: '用户消息',
    session_id: 'session_123',
    nonce: wpNonce
  })
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('AI回复:', data.data.response);
  }
});
```

### WebSocket连接示例
```python
import asyncio
import websockets
import json

async def admin_websocket():
    uri = "wss://chat.22668.xyz:8000/ws/admin/admin_id"
    headers = {"Authorization": "Bearer YOUR_TOKEN"}

    async with websockets.connect(uri, extra_headers=headers) as websocket:
        async for message in websocket:
            data = json.loads(message)
            print(f"收到消息: {data}")
```

## 🔧 故障排除

**常见问题**:
- **连接失败**: 检查API Token和服务器地址配置
- **代理问题**: 验证SOCKS5代理设置
- **插件不显示**: 确认WordPress插件正确激活
- **客服端连接失败**: 检查WebSocket连接和Token配置
- **Token错误**: 使用 `python token_manager.py` 重新生成Token
- **数据库问题**: 使用 `python clear_database.py` 清理数据库

**调试方法**:
- 查看控制台日志输出
- 检查网络连接状态
- 验证环境变量配置
- 测试API接口响应

## 🤖 SalesGPT Agent - Dylan AI销售助手

系统已升级为基于SalesGPT的智能销售助手Dylan，专为KNKA环保电器提供专业的销售服务：

### 🚀 核心特性

**智能销售流程管理**：
- **阶段化销售**: 介绍 → 资格确认 → 需求分析 → 方案展示 → 异议处理 → 成交
- **对话状态跟踪**: 自动识别和管理销售对话的当前阶段
- **个性化推荐**: 基于客户需求智能推荐合适的产品

**专业产品咨询**：
- **KNKA产品专家**: 深度了解除湿机和空气净化器产品线
- **技术参数解答**: 专业回答CADR值、噪音水平、适用面积等技术问题
- **使用场景匹配**: 根据客户的使用环境推荐最适合的产品

**智能客服转接**：
- **自动识别复杂问题**: 检测售后问题、投诉等需要人工处理的情况
- **无缝转接**: 自动收集客户信息并转接给人工客服
- **上下文保持**: 转接时保留完整的对话历史和客户需求

### ⚙️ 配置文件

**SalesGPT配置** (`backend/salesgpt_config/`):
```
salesgpt_config/
├── product_catalog.txt     # 产品目录和详细信息
└── README.md              # 配置说明文档
```

**产品目录配置** (`backend/salesgpt_config/product_catalog.txt`):
```
KNKA Environmental Appliances - Product Catalog

=== DEHUMIDIFIERS ===
1. KNKA PD22SC Dehumidifier
   - Capacity: 22L/day
   - Coverage: Up to 300 sq ft
   - Noise Level: <42dB
   - Features: Auto-defrost, continuous drainage
   - Price Range: $200-250
   - Best for: Medium rooms, bedrooms

2. KNKA D036 Commercial Dehumidifier
   - Capacity: 36L/day
   - Coverage: Up to 500 sq ft
   - Noise Level: <45dB
   - Features: Heavy-duty, commercial grade
   - Price Range: $350-400
   - Best for: Large spaces, basements

=== AIR PURIFIERS ===
1. KNKA APH3000 HEPA Air Purifier
   - CADR: 300 m³/h
   - Coverage: Up to 400 sq ft
   - Filtration: True HEPA + Activated Carbon
   - Noise Level: <35dB
   - Price Range: $180-220
   - Best for: Living rooms, offices

2. KNKA AP2000 Compact Air Purifier
   - CADR: 200 m³/h
   - Coverage: Up to 250 sq ft
   - Filtration: HEPA + Pre-filter
   - Noise Level: <30dB
   - Price Range: $120-150
   - Best for: Bedrooms, small spaces
```

### 🎯 销售阶段管理

**阶段定义**:
1. **Introduction**: 介绍自己和公司，建立初步联系
2. **Qualification**: 确认客户是否需要空气质量解决方案
3. **Value Proposition**: 简要说明KNKA产品的价值和优势
4. **Needs Analysis**: 深入了解客户的具体需求和痛点
5. **Solution Presentation**: 展示最适合客户需求的产品方案
6. **Objection Handling**: 处理客户的疑虑和异议
7. **Close**: 引导客户完成购买或下一步行动
8. **End**: 结束对话

**阶段转换逻辑**:
- 系统自动分析用户消息和AI回复
- 智能判断当前对话应该进入的下一个阶段
- 根据销售阶段调整回复策略和重点

### 🔧 环境变量配置

在 `backend/.env` 文件中添加SalesGPT相关配置：

```env
# SalesGPT配置
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.0-flash-lite

# 代理配置（如需要）
PROXY_ENABLED=true
PROXY_URL=socks5://**************:33489

# Dylan助手配置
SALESPERSON_NAME=Dylan
COMPANY_NAME=KNKA Environmental Appliances
COMPANY_WEBSITE=knkalife.com
```

### 🚀 使用方法

**1. 启动SalesGPT服务**:
```bash
cd backend

# 确保在虚拟环境中
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 启动服务（SalesGPT会自动加载）
python main.py
```

**2. 验证SalesGPT状态**:
启动后查看日志输出：
```
🔄 Agent configuration loaded (personality, knowledge_base, intents, entities)
✅ SalesGPT service initialized with Gemini model: gemini-2.0-flash-lite
📦 Product catalog loaded successfully
🚀 Uvicorn running on http://0.0.0.0:8000
```

**3. 测试销售对话**:
通过WordPress前端或客服端测试Dylan的销售能力：

```
用户: "Hi, I'm looking for an air purifier"
Dylan: "Hello! I'm Dylan, your AI assistant at KNKA Environmental Appliances.
       I'd be happy to help you find the perfect air purifier!
       Could you tell me about the space you're looking to purify?"

用户: "It's for my bedroom, about 250 sq ft"
Dylan: "Perfect! For a 250 sq ft bedroom, I'd recommend our KNKA AP2000 Compact Air Purifier.
       It has a CADR of 200 m³/h, which is ideal for your space size, and operates at less than 30dB -
       very quiet for bedroom use. Would you like to know more about its features?"
```

### 🔄 人工客服转接

**自动转接触发条件**:
- 用户明确要求人工客服 ("I want to speak to a human")
- 检测到售后问题关键词 ("broken", "defective", "return")
- 包含订单信息的投诉 ("order #12345 has a problem")

**转接流程**:
1. Dylan识别需要人工介入的情况
2. 自动收集客户信息和问题描述
3. 设置会话为管理员控制状态
4. 通知客服端有新的转接请求
5. 人工客服接管对话

**转接示例**:
```
用户: "My order #12345 dehumidifier is making loud noises"
Dylan: "I'm connecting you with our customer service team for assistance with your order.
       Please wait a moment while I transfer you to a specialist who can help resolve this issue."

[系统自动转接给人工客服]
```

### 📊 对话历史和状态管理

**会话状态跟踪**:
- 每个会话维护独立的销售阶段状态
- 保留最近20轮对话历史用于上下文理解
- 支持会话重置和状态清理

**状态查询API**:
```python
# 获取会话销售阶段
GET /api/salesgpt/session/{session_id}/stage

# 获取会话摘要
GET /api/salesgpt/session/{session_id}/summary

# 重置会话状态
POST /api/salesgpt/session/{session_id}/reset
```

### 🛠️ 高级配置

**自定义产品目录**:
编辑 `backend/salesgpt_config/product_catalog.txt` 添加新产品：
```
=== NEW PRODUCT CATEGORY ===
Product Name: KNKA NewModel
- Specifications...
- Features...
- Price Range...
- Best for...
```

**调整销售策略**:
在 `backend/services/salesgpt_service.py` 中修改：
- `build_system_prompt()`: 调整销售指导原则
- `analyze_conversation_stage()`: 修改阶段转换逻辑
- `should_handover_to_human()`: 调整转接触发条件

**LLM参数优化**:
```python
# 在 salesgpt_service.py 中调整
self.llm_config = {
    "model": "gemini-2.0-flash-lite",
    "temperature": 0.7,        # 创造性 (0.1-1.0)
    "max_tokens": 1000,        # 回复长度
}
```

### 🔍 故障排除

**常见问题**:

1. **SalesGPT无响应**:
   - 检查Gemini API密钥是否正确
   - 验证网络连接和代理设置
   - 查看后端日志中的错误信息

2. **产品目录未加载**:
   - 确认 `backend/salesgpt_config/product_catalog.txt` 文件存在
   - 检查文件编码为UTF-8
   - 重启后端服务

3. **销售阶段不更新**:
   - 检查对话历史是否正常保存
   - 验证阶段转换逻辑是否触发
   - 查看会话状态管理日志

**调试命令**:
```bash
# 查看SalesGPT服务状态
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/salesgpt/status

# 测试产品目录加载
python -c "
from services.salesgpt_service import get_salesgpt_service
service = get_salesgpt_service()
print(service.product_catalog[:200])
"

# 查看会话销售阶段
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/salesgpt/session/SESSION_ID/stage
```

### 📈 性能监控

**关键指标**:
- 销售阶段转换成功率
- 人工转接触发频率
- 平均对话轮数
- 客户满意度反馈

**日志监控**:
```bash
# 查看SalesGPT相关日志
tail -f backend/logs/backend.log | grep -i "salesgpt\|dylan\|stage"

# 监控转接请求
tail -f backend/logs/backend.log | grep -i "handover\|transfer"
```

## 🧠 长期偏好记忆系统

系统具备智能的长期偏好学习能力，为用户提供个性化的服务体验：

### 🎯 核心功能

**自动偏好学习**：
- 从用户对话中自动提取偏好信号
- 无需用户主动设置，智能识别用户需求
- 支持多维度偏好分析和学习

**个性化响应**：
- 基于学习到的偏好生成个性化回复
- 优先推荐用户感兴趣的产品类型
- 调整沟通风格以匹配用户偏好

**跨会话记忆**：
- 同一设备/浏览器的长期偏好保持
- 用户再次访问时自动应用已学习的偏好
- 渐进式优化，使用越多体验越好

### 📊 偏好学习维度

**产品兴趣**：
- 除湿机、空气净化器等产品类型偏好
- 特定型号的关注度分析
- 产品功能需求识别

**使用场景**：
- 卧室、客厅、办公室等使用环境
- 家庭、商业等应用场景
- 空间大小和需求分析

**关注重点**：
- 噪音水平、价格、效果、质量等关注点
- 技术参数需求（CADR值、分贝数等）
- 购买决策因素分析

**沟通偏好**：
- 简单直接 vs 详细解释
- 技术性 vs 通俗易懂
- 专业程度和理解能力评估

**价格敏感度**：
- 预算范围识别
- 价格关注程度分析
- 性价比需求评估

### 🔧 技术实现

**数据存储**：
```sql
-- 用户偏好表结构
user_preferences (
  user_id: 基于session_id生成的唯一标识
  profile: 用户基本偏好（沟通风格、专业程度等）
  interests: 兴趣和产品偏好
  history: 历史行为记录
  learning_data: 学习统计数据
)
```

**偏好识别**：
- 关键词频率分析
- 问题类型模式识别
- 对话深度和技术需求分析
- 价格敏感度信号检测

**个性化上下文**：
```
个性化提示：用户喜欢详细的解释和具体的技术参数。
用户对除湿机产品特别感兴趣。用户特别关心产品的噪音水平。
用户对价格比较敏感。
```

### 📈 使用效果

**老用户体验**：
- AI记住用户的产品偏好和关注点
- 自动调整回复风格和详细程度
- 优先推荐符合偏好的产品和解决方案

**新用户体验**：
- 获得标准的专业服务体验
- 系统开始学习和记录偏好
- 逐步建立个性化服务档案

**商业价值**：
- 提高用户满意度和转化率
- 增强用户粘性和回访率
- 提供更精准的产品推荐

### ⚠️ 重要说明

**会话范围**：
- 偏好记忆基于浏览器指纹生成的session_id
- 同一设备同一浏览器：保持偏好记忆 ✅
- 换设备或浏览器：重新开始学习 ❌
- 不支持跨设备偏好同步

**隐私保护**：
- 只学习产品相关偏好，不涉及个人隐私
- 数据存储在本地数据库，不上传第三方
- 用户可通过清理数据库重置偏好

**数据管理**：
```bash
# 查看偏好学习效果
cd backend
python test_preference_database.py

# 清理所有偏好数据
python clear_database.py
```

## 🌐 访问地址

- **后端API**: https://chat.22668.xyz:8000
- **WordPress插件**: 安装后在网站前端显示聊天按钮
- **客服端**: 桌面应用，使用admin/admin登录

## 🔒 安全特性

- Bearer Token认证
- SOCKS5代理支持
- 数据加密传输
- 会话隔离
- 管理员权限控制

## 🛠️ 实用工具

### 后端工具
- **Token管理**: `python token_manager.py` - 生成和管理安全Token
- **数据库清理**: `python clear_database.py` - 清理聊天数据库
- **SalesGPT测试**: 通过前端界面测试Dylan销售助手功能
- **产品目录管理**: 编辑 `salesgpt_config/product_catalog.txt` 更新产品信息
- **IP黑名单服务**: 自动更新和管理IP黑名单
- **偏好测试**: `python test_preferences.py` - 测试长期偏好学习功能
- **偏好数据库**: `python test_preference_database.py` - 查看偏好学习效果

### 客服端工具
- **快速配置**: `python quick_config.py [local|lan|remote]` - 快速环境配置
- **配置助手**: `python config_helper.py` - 交互式配置向导
- **批处理配置**: `config.bat` - Windows图形化配置
- **用户管理**: `python user_manager.py` - 管理客服账户

### WordPress插件功能
- **聊天界面**: 悬浮聊天按钮，支持多位置
- **后台设置**: 完整的配置界面
- **安全管理**: API防护和IP黑名单管理
- **会话管理**: 聊天历史和会话控制

## 🛠️ 技术栈

- **后端**: FastAPI, SQLAlchemy, WebSockets, SQLite
- **前端**: JavaScript, CSS3, HTML5
- **客服端**: PyQt6, asyncio, websockets
- **数据库**: SQLite (支持用户偏好存储)
- **LLM**: Google Gemini, OpenAI GPT, DeepSeek
- **SalesGPT**: LangChain, 自定义销售流程管理
- **代理**: SOCKS5代理支持
- **安全**: Bearer Token认证，多层API防护
- **AI增强**: 长期偏好学习，个性化响应生成，智能销售助手

## 📄 许可证

MIT License

## 👨‍💻 作者

jiang - YF AI Chat Now

---

如需技术支持，请联系：<EMAIL>