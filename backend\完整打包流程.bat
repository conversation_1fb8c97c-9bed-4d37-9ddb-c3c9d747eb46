@echo off
chcp 65001 >nul
title YF AI Chat Backend v2 - 完整打包流程

echo.
echo ======================================================================
echo   YF AI Chat Backend v2 - 完整打包和测试流程
echo ======================================================================
echo.
echo 🚀 v2版本特色:
echo    ⚡ Windows高性能模式自动优化
echo    🛡️  防止系统进入效能模式
echo    🔧 智能连接错误处理和过滤
echo    📊 增强的性能监控和状态显示
echo    🎯 优化的网络参数和并发处理
echo    🌐 改进的WordPress插件通信性能
echo.

:: 设置错误处理
setlocal enabledelayedexpansion

:: 记录开始时间
set start_time=%time%
echo 🕐 开始时间: %start_time%
echo.

:: 步骤1: 环境检查
echo ==================== 步骤1: 环境检查 ====================

if not exist ".venv\Scripts\activate.bat" (
    echo ❌ 虚拟环境不存在
    echo.
    echo 正在创建虚拟环境...
    python -m venv .venv
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        goto :error
    )
    echo ✅ 虚拟环境创建成功
)

echo 📦 激活虚拟环境...
call .venv\Scripts\activate.bat

echo.
echo ==================== 步骤2: 依赖安装 ====================

echo 🔍 检查和安装依赖包...
pip install --upgrade pip >nul 2>&1

:: 安装打包依赖
pip install pyinstaller >nul 2>&1
if errorlevel 1 (
    echo ❌ PyInstaller安装失败
    goto :error
)

:: 安装项目依赖
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 项目依赖安装失败
    goto :error
)

echo ✅ 依赖安装完成

echo.
echo ==================== 步骤3: 清理构建 ====================

echo 🧹 清理旧的构建文件...
if exist "build" (
    rmdir /s /q "build" >nul 2>&1
    echo    🗑️ 已删除 build 目录
)

if exist "dist\YF_AI_Chat_Backend_v2.exe" (
    del "dist\YF_AI_Chat_Backend_v2.exe" >nul 2>&1
    echo    🗑️ 已删除旧的exe文件
)

if exist "dist\YF_AI_Chat_Backend_v2_便携版" (
    rmdir /s /q "dist\YF_AI_Chat_Backend_v2_便携版" >nul 2>&1
    echo    🗑️ 已删除旧的便携版目录
)

:: 清理Python缓存
for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d" >nul 2>&1

echo ✅ 清理完成

echo.
echo ==================== 步骤4: 执行打包 ====================

echo 📦 开始打包v2版本...
echo 💡 这可能需要几分钟时间，请耐心等待...
echo.

:: 记录打包开始时间
set build_start=%time%

python build_v2.py
set build_result=%errorlevel%

:: 记录打包结束时间
set build_end=%time%

if %build_result% neq 0 (
    echo ❌ 打包失败！
    goto :error
)

echo ✅ 打包完成

echo.
echo ==================== 步骤5: 验证打包结果 ====================

echo 🔍 验证打包文件...

if exist "dist\YF_AI_Chat_Backend_v2.exe" (
    echo ✅ 主程序文件存在
    
    :: 获取文件大小
    for %%F in ("dist\YF_AI_Chat_Backend_v2.exe") do (
        set /a size_mb=%%~zF/1024/1024
        echo    📊 文件大小: !size_mb!MB
    )
) else (
    echo ❌ 主程序文件不存在
    goto :error
)

if exist "dist\YF_AI_Chat_Backend_v2_便携版" (
    echo ✅ 便携版目录存在
    
    :: 检查便携版内容
    if exist "dist\YF_AI_Chat_Backend_v2_便携版\YF_AI_Chat_Backend_v2.exe" (
        echo    ✅ 便携版exe文件
    ) else (
        echo    ❌ 便携版exe文件缺失
    )
    
    if exist "dist\YF_AI_Chat_Backend_v2_便携版\启动高性能模式.bat" (
        echo    ✅ 高性能启动脚本
    ) else (
        echo    ❌ 高性能启动脚本缺失
    )
    
    if exist "dist\YF_AI_Chat_Backend_v2_便携版\README_v2.txt" (
        echo    ✅ 使用说明文档
    ) else (
        echo    ❌ 使用说明文档缺失
    )
    
) else (
    echo ❌ 便携版目录不存在
    goto :error
)

echo.
echo ==================== 步骤6: 功能测试 ====================

echo 🧪 运行功能测试...
python test_v2_package.py

echo.
echo ==================== 完成总结 ====================

:: 计算总耗时
set end_time=%time%
echo 🕐 开始时间: %start_time%
echo 🕐 结束时间: %end_time%

echo.
echo 🎉 YF AI Chat Backend v2 打包流程完成！
echo ======================================================================
echo.
echo 📁 输出文件:
echo    📄 dist\YF_AI_Chat_Backend_v2.exe
echo    📁 dist\YF_AI_Chat_Backend_v2_便携版\
echo    📄 dist\v2_test_report.txt
echo.
echo 🚀 使用方法:
echo    1. 进入 dist\YF_AI_Chat_Backend_v2_便携版\ 目录
echo    2. 复制 .env.example 为 .env 并配置API密钥
echo    3. 右键以管理员身份运行 "启动高性能模式.bat"
echo.
echo 💡 v2版本优势:
echo    ⚡ 自动优化Windows电源设置，解决效能模式问题
echo    🛡️  持续保持高性能状态，防止性能下降
echo    🔧 智能处理网络连接错误，减少无关警告
echo    📊 实时性能监控，随时了解系统状态
echo    🎯 优化网络参数，提升WordPress插件通信速度
echo.
echo ======================================================================

:: 询问是否打开输出目录
echo.
echo 是否打开输出目录查看结果？^(Y/N^)
set /p open_dir=
if /i "!open_dir!"=="Y" (
    explorer "dist"
)

echo.
echo 👋 打包流程完成，感谢使用YF AI Chat Backend v2！
pause
goto :end

:error
echo.
echo ❌ 打包流程失败！
echo 请检查上面的错误信息并解决问题后重试
echo.
pause
exit /b 1

:end
exit /b 0
