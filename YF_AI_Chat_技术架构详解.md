# 🏗️ YF AI Chat 技术架构详解

## 📋 系统架构总览

```
┌─────────────────────────────────────────────────────────────────┐
│                    YF AI Chat 智能客服系统                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │  前端接入层  │    │  AI处理层   │    │  管理控制层  │         │
│  │             │    │             │    │             │         │
│  │ WordPress   │◄──►│ SalesGPT    │◄──►│ 客服管理端   │         │
│  │ Shopify     │    │ 多LLM引擎   │    │ 实时接管     │         │
│  │ 聊天界面    │    │ 智能路由    │    │ 会话监控     │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│         │                   │                   │              │
│         └───────────────────┼───────────────────┘              │
│                             │                                  │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │  安全防护层  │    │  数据存储层  │    │  系统管理层  │         │
│  │             │    │             │    │             │         │
│  │ Token认证   │    │ SQLite DB   │    │ 配置管理     │         │
│  │ API防护     │    │ 会话数据    │    │ 日志监控     │         │
│  │ IP黑名单    │    │ 用户偏好    │    │ 性能统计     │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🌐 前端接入层

### WordPress插件架构
```
WordPress网站
├── yf-ai-chat-now/                    # 插件目录
│   ├── yf-ai-chat-now.php            # 主插件文件
│   ├── assets/                        # 静态资源
│   │   ├── css/chat-widget.css       # 聊天窗口样式
│   │   └── js/chat-widget.js         # 聊天交互逻辑
│   ├── includes/                      # 核心功能
│   │   ├── class-chat-handler.php    # 聊天处理类
│   │   └── class-api-client.php      # API客户端
│   └── admin/                         # 管理界面
│       └── settings.php               # 配置页面
```

### Shopify应用架构
```
Shopify主题集成
├── theme.liquid                       # 主题模板
│   └── {% include 'yf-chat-widget' %} # 聊天组件
├── snippets/
│   └── yf-chat-widget.liquid         # 聊天窗口代码
├── assets/
│   ├── yf-chat.css                   # 样式文件
│   └── yf-chat.js                    # 交互脚本
└── config/
    └── settings_schema.json          # 配置选项
```

### 前端技术特点
- **原生JavaScript**: 无框架依赖，兼容性强
- **响应式设计**: 自适应PC和移动端
- **主题集成**: 无缝融入现有网站设计
- **性能优化**: 懒加载，最小化资源占用

---

## 🤖 AI处理层

### SalesGPT核心引擎
```python
# 核心AI处理流程
class SalesGPTService:
    def __init__(self):
        self.llm_providers = {
            'gemini': GeminiProvider(),
            'openai': OpenAIProvider(), 
            'deepseek': DeepSeekProvider()
        }
        self.conversation_chain = ConversationChain()
        self.product_knowledge = ProductKnowledge()
    
    async def process_message(self, message, context):
        # 1. 意图识别
        intent = await self.classify_intent(message)
        
        # 2. 上下文构建
        context = self.build_context(message, context)
        
        # 3. 知识检索
        knowledge = self.retrieve_knowledge(intent, context)
        
        # 4. 响应生成
        response = await self.generate_response(
            message, context, knowledge
        )
        
        return response
```

### 多LLM智能路由
```python
class LLMRouter:
    def select_provider(self, message_type, complexity):
        if message_type == 'product_inquiry':
            return 'gemini'  # 产品咨询用Gemini
        elif complexity == 'high':
            return 'openai'  # 复杂问题用OpenAI
        else:
            return 'deepseek'  # 一般问题用DeepSeek
```

### AI能力矩阵
| 功能模块 | Gemini | OpenAI | DeepSeek | 使用场景 |
|---------|--------|--------|----------|----------|
| 产品咨询 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 主力模型 |
| 技术支持 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 复杂问题 |
| 闲聊对话 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 日常交流 |
| 销售引导 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 转化优化 |

---

## 👥 管理控制层

### 客服管理端架构
```python
# 客服端主要模块
class CustomerServiceClient:
    def __init__(self):
        self.session_manager = SessionManager()
        self.message_handler = MessageHandler()
        self.admin_controller = AdminController()
        self.websocket_manager = WebSocketManager()
    
    # 实时会话接管
    async def take_control(self, session_id, admin_id):
        await self.session_manager.set_admin_control(
            session_id, True, admin_id
        )
        await self.notify_ai_engine(session_id, 'human_takeover')
    
    # 自动超时释放
    async def auto_release_control(self, session_id):
        timeout = 15 * 60  # 15分钟
        await asyncio.sleep(timeout)
        await self.session_manager.set_admin_control(
            session_id, False
        )
```

### 实时通信架构
```
WebSocket连接管理
├── 客服端连接池
│   ├── admin_1: WebSocket连接
│   ├── admin_2: WebSocket连接
│   └── admin_n: WebSocket连接
├── 用户端连接池
│   ├── session_1: WebSocket连接
│   ├── session_2: WebSocket连接
│   └── session_n: WebSocket连接
└── 消息路由器
    ├── 广播消息
    ├── 点对点消息
    └── 状态同步
```

---

## 🔐 安全防护层

### 三级Token认证体系
```python
# Token权限映射
TOKEN_PERMISSIONS = {
    "wordpress": ["chat"],                    # WordPress插件权限
    "admin": ["chat", "admin", "websocket"],  # 管理员权限
    "api": ["chat", "admin", "websocket"]     # API完整权限
}

# 权限验证流程
def verify_token(required_permission="chat"):
    def _verify_token(credentials):
        token = credentials.credentials
        
        # 1. Token类型识别
        token_type = identify_token_type(token)
        
        # 2. 权限检查
        if required_permission not in TOKEN_PERMISSIONS[token_type]:
            raise HTTPException(403, "Insufficient permissions")
        
        return {"token": token, "type": token_type}
    
    return _verify_token
```

### API防护系统
```python
class ProtectionService:
    def __init__(self):
        self.ip_blacklist = IPBlacklistService()
        self.rate_limiter = RateLimiter()
        self.content_filter = ContentFilter()
    
    async def check_message_protection(self, message, ip, session_id):
        # 1. IP黑名单检查
        if not await self.ip_blacklist.is_allowed(ip):
            return False, "IP blocked"
        
        # 2. 频率限制检查
        if not self.rate_limiter.check_rate(ip):
            return False, "Rate limit exceeded"
        
        # 3. 内容过滤检查
        if not self.content_filter.is_safe(message):
            return False, "Content filtered"
        
        return True, ""
```

---

## 📊 数据存储层

### 数据库设计
```sql
-- 核心数据表结构
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    admin_control BOOLEAN DEFAULT FALSE,
    admin_control_by TEXT,
    user_ip TEXT,
    archived BOOLEAN DEFAULT FALSE
);

CREATE TABLE chat_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    content TEXT NOT NULL,
    sender TEXT NOT NULL,  -- 'user', 'ai', 'admin'
    admin_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE form_submissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    category TEXT NOT NULL,
    form_data JSON NOT NULL,
    status INTEGER DEFAULT 0,  -- 0=未处理, 1=已处理
    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 数据管理工具
```
数据库管理器功能
├── 连接管理
│   ├── 数据库路径选择
│   ├── 历史记录保存
│   └── 快速连接后端DB
├── 数据查看
│   ├── 表格分页显示
│   ├── 数据筛选排序
│   └── 实时编辑功能
├── SQL查询
│   ├── 语法高亮编辑器
│   ├── 查询历史管理
│   └── 结果导出功能
└── 数据导出
    ├── CSV格式导出
    ├── Excel格式导出
    └── JSON格式导出
```

---

## ⚙️ 系统管理层

### 配置管理系统
```python
# 配置管理架构
class ConfigManager:
    def __init__(self):
        self.config_sources = [
            EnvironmentConfig(),    # 环境变量
            FileConfig(),          # 配置文件
            DatabaseConfig(),      # 数据库配置
            DefaultConfig()        # 默认配置
        ]
    
    def get_config(self, key):
        for source in self.config_sources:
            if source.has_key(key):
                return source.get(key)
        return None
```

### 监控和日志系统
```python
# 日志管理
class LogManager:
    def __init__(self):
        self.loggers = {
            'api': self.setup_api_logger(),
            'ai': self.setup_ai_logger(),
            'admin': self.setup_admin_logger(),
            'security': self.setup_security_logger()
        }
    
    def log_api_request(self, request, response, duration):
        self.loggers['api'].info({
            'method': request.method,
            'url': request.url,
            'status': response.status_code,
            'duration': duration,
            'timestamp': datetime.now()
        })
```

---

## 🚀 部署架构

### 容器化部署
```dockerfile
# Dockerfile示例
FROM python:3.12-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 生产环境架构
```
生产部署架构
├── 负载均衡器 (Nginx)
│   ├── SSL终端
│   ├── 静态文件服务
│   └── 反向代理
├── 应用服务器集群
│   ├── Backend实例1
│   ├── Backend实例2
│   └── Backend实例N
├── 数据库服务
│   ├── 主数据库
│   ├── 备份数据库
│   └── 数据同步
└── 监控服务
    ├── 性能监控
    ├── 日志收集
    └── 告警系统
```

---

## 📈 性能指标

### 系统性能基准
- **并发用户**: 1000+ 同时在线
- **响应时间**: 平均 < 500ms
- **可用性**: 99.9% SLA
- **数据库**: 支持百万级记录
- **AI处理**: 平均 2-5秒响应

### 扩展性设计
- **水平扩展**: 支持多实例部署
- **垂直扩展**: 支持硬件升级
- **缓存策略**: Redis缓存热点数据
- **CDN加速**: 静态资源全球分发

---

*本技术架构设计确保了系统的高性能、高可用性和高扩展性，为商业化部署提供了坚实的技术基础。*
