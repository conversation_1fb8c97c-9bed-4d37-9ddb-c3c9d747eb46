@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    YF AI Chat 客服端打包工具
echo ========================================
echo.

cd /d "%~dp0"

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装或未添加到PATH
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)

echo 正在检查依赖包...
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo [ERROR] PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo.
echo 开始打包...
python build.py

if errorlevel 1 (
    echo.
    echo [ERROR] 打包失败，请检查错误信息
) else (
    echo.
    echo [SUCCESS] 打包成功！
    echo.
    echo [INFO] 输出目录: dist\
    echo [INFO] 可执行文件: dist\YF_AI_Chat_Client.exe
    echo [INFO] 配置文件已复制到dist目录
    echo.
    echo 您可以将整个dist目录复制到任何位置使用
)

echo.
pause
