@echo off
chcp 65001 >nul
title YF AI Chat Backend - 高性能模式

echo.
echo ============================================================
echo   YF AI Chat Backend - 高性能模式启动
echo ============================================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 检测到管理员权限
    set ADMIN_MODE=1
) else (
    echo ⚠️  未检测到管理员权限
    echo 💡 建议右键选择"以管理员身份运行"以获得最佳性能
    set ADMIN_MODE=0
    echo.
    echo 是否继续？^(Y/N^)
    set /p continue=
    if /i not "%continue%"=="Y" (
        echo 已取消启动
        pause
        exit /b 1
    )
)

echo.
echo 🔧 正在应用高性能优化...

:: 设置高性能电源计划
if %ADMIN_MODE%==1 (
    echo ⚡ 设置高性能电源计划...
    powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c >nul 2>&1
    if %errorLevel% == 0 (
        echo ✅ 高性能电源计划已启用
    ) else (
        echo ⚠️  电源计划设置失败
    )
    
    :: 优化电源设置
    echo 🚀 优化电源参数...
    
    :: 禁用USB选择性暂停
    powercfg /setacvalueindex 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c 2a737441-1930-4402-8d77-b2bebba308a3 48e6b7a6-50f5-4782-a5d4-53bb8f07e226 0 >nul 2>&1
    
    :: 设置处理器最小状态100%
    powercfg /setacvalueindex 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 100 >nul 2>&1
    
    :: 设置处理器最大状态100%
    powercfg /setacvalueindex 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100 >nul 2>&1
    
    :: 应用设置
    powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c >nul 2>&1
    
    echo ✅ 电源优化完成
) else (
    echo ℹ️  跳过电源设置^(需要管理员权限^)
)

:: 设置进程优先级为高
echo 🎯 设置进程优先级...
wmic process where name="python.exe" CALL setpriority "high priority" >nul 2>&1
wmic process where name="YF_AI_Chat_Backend.exe" CALL setpriority "high priority" >nul 2>&1

echo.
echo 🔍 检查环境...

:: 检查虚拟环境（如果是源码运行）
if exist ".venv\Scripts\activate.bat" (
    echo 📦 激活虚拟环境...
    call .venv\Scripts\activate.bat
    
    :: 检查依赖
    python -c "import fastapi, uvicorn" >nul 2>&1
    if errorlevel 1 (
        echo ❌ 依赖缺失，正在安装...
        pip install -r requirements.txt
        if errorlevel 1 (
            echo ❌ 依赖安装失败
            pause
            exit /b 1
        )
    )
    
    echo ✅ 环境检查完成
    set START_COMMAND=python start_high_performance.py
) else (
    :: 检查是否有打包的exe文件
    if exist "dist\YF_AI_Chat_Backend.exe" (
        echo 📦 检测到打包版本
        set START_COMMAND=dist\YF_AI_Chat_Backend.exe
    ) else (
        echo 📦 使用Python直接运行
        set START_COMMAND=python start_high_performance.py
    )
)

echo.
echo ============================================================
echo 🎯 高性能优化已启用:
echo    ⚡ Windows高性能电源计划
echo    🚀 进程高优先级
echo    🛡️  防止进入效能模式
echo    🔄 优化的连接参数
echo    📊 增强的并发处理
echo    🌐 改进的网络性能
echo ============================================================
echo.
echo 🚀 启动高性能后端服务...
echo 💡 按 Ctrl+C 停止服务
echo.

:: 启动服务
%START_COMMAND%

echo.
echo 🔄 正在恢复默认电源设置...

:: 恢复平衡电源模式
if %ADMIN_MODE%==1 (
    powercfg /setactive 381b4222-f694-41f0-9685-ff5bb260df2e >nul 2>&1
    if %errorLevel% == 0 (
        echo ✅ 已恢复平衡电源模式
    )
)

echo.
echo 👋 服务已停止
pause
