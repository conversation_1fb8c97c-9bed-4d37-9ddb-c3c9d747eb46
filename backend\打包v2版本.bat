@echo off
chcp 65001 >nul
title YF AI Chat Backend v2 打包工具

echo.
echo ======================================================================
echo   YF AI Chat Backend v2 - 高性能版本打包工具
echo ======================================================================
echo.
echo 🚀 v2版本新功能:
echo    ⚡ Windows高性能模式自动优化
echo    🛡️  防止系统进入效能模式
echo    🔧 智能连接错误处理
echo    📊 增强的性能监控
echo    🎯 优化的网络参数
echo.

:: 检查虚拟环境
if not exist ".venv\Scripts\activate.bat" (
    echo ❌ 虚拟环境不存在
    echo.
    echo 请先创建虚拟环境:
    echo    python -m venv .venv
    echo    .venv\Scripts\activate
    echo    pip install -r requirements.txt
    echo.
    pause
    exit /b 1
)

echo 📦 激活虚拟环境...
call .venv\Scripts\activate.bat

echo.
echo 🔍 检查依赖包...

:: 检查PyInstaller
python -c "import PyInstaller; print('PyInstaller版本:', PyInstaller.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

:: 检查其他依赖
python -c "import fastapi, uvicorn, pydantic, sqlalchemy, aiosqlite, langchain, psutil" 2>nul
if errorlevel 1 (
    echo ❌ 依赖包缺失，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查完成

echo.
echo 🧹 清理旧的构建文件...
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "dist\YF_AI_Chat_Backend_v2.exe" del "dist\YF_AI_Chat_Backend_v2.exe" >nul 2>&1
if exist "dist\YF_AI_Chat_Backend_v2_便携版" rmdir /s /q "dist\YF_AI_Chat_Backend_v2_便携版" >nul 2>&1

echo.
echo 📦 开始打包v2版本...
echo 💡 这可能需要几分钟时间，请耐心等待...
echo.

:: 运行打包脚本
python build_v2.py

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 请检查上面的错误信息
    pause
    exit /b 1
)

echo.
echo 🎉 打包完成！
echo.
echo 📁 输出文件位置:
echo    dist\YF_AI_Chat_Backend_v2.exe
echo    dist\YF_AI_Chat_Backend_v2_便携版\
echo.

:: 检查文件是否存在
if exist "dist\YF_AI_Chat_Backend_v2.exe" (
    echo ✅ 主程序文件: YF_AI_Chat_Backend_v2.exe
    
    :: 显示文件大小
    for %%F in ("dist\YF_AI_Chat_Backend_v2.exe") do (
        set /a size=%%~zF/1024/1024
        echo    文件大小: !size!MB
    )
) else (
    echo ❌ 主程序文件未找到
)

if exist "dist\YF_AI_Chat_Backend_v2_便携版" (
    echo ✅ 便携版目录: YF_AI_Chat_Backend_v2_便携版
    echo    包含启动脚本和说明文档
) else (
    echo ❌ 便携版目录未找到
)

echo.
echo 🚀 使用方法:
echo    1. 进入 dist\YF_AI_Chat_Backend_v2_便携版\ 目录
echo    2. 复制 .env.example 为 .env 并配置
echo    3. 右键以管理员身份运行 "启动高性能模式.bat"
echo.
echo 💡 v2版本特色:
echo    ⚡ 自动优化Windows电源设置
echo    🛡️  防止进入效能模式，保持最佳性能
echo    🔧 智能处理连接错误，减少无关警告
echo    📊 实时性能监控和状态显示
echo.

:: 询问是否打开输出目录
echo 是否打开输出目录？^(Y/N^)
set /p open_dir=
if /i "%open_dir%"=="Y" (
    explorer "dist"
)

echo.
echo 👋 打包完成，感谢使用！
pause
