@echo off
chcp 65001 >nul
echo 🔐 YF AI Chat 数据库管理器打包工具
echo ========================================
echo.

REM 检查是否安装了必要的包
python -c "import PyQt6, pandas, pyinstaller" 2>nul
if errorlevel 1 (
    echo ❌ 缺少必要的依赖包!
    echo 正在安装依赖包...
    pip install PyQt6 pandas pyinstaller
    if errorlevel 1 (
        echo ❌ 依赖包安装失败!
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装成功!
    echo.
)

REM 执行打包
echo 🚀 开始打包...
python build_database_manager.py

echo.
echo 打包完成! 按任意键退出...
pause >nul
