#!/usr/bin/env python3
"""
YF AI Chat Backend 打包脚本
使用PyInstaller将后端应用程序打包为独立的可执行文件

使用方法:
python build.py

输出:
- dist/YF_AI_Chat_Backend.exe (Windows可执行文件)
- dist/YF_AI_Chat_Backend_便携版/ (包含所有依赖的文件夹)
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_requirements():
    """检查打包所需的依赖"""
    print("[INFO] 检查打包环境...")

    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"[OK] PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("[ERROR] PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False

    # 检查必要的文件
    required_files = [
        "main.py",
        "requirements.txt",
        ".env.example"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print(f"[ERROR] 缺少必要文件: {missing_files}")
        return False

    print("[OK] 打包环境检查通过")
    return True


def clean_build_dirs():
    """清理之前的构建目录"""
    print("[INFO] 清理构建目录...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"  已删除: {dir_name}")
    
    # 删除spec文件
    spec_files = list(Path(".").glob("*.spec"))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"  已删除: {spec_file}")


def create_spec_file():
    """创建PyInstaller配置文件"""
    print("📝 创建PyInstaller配置文件...")

    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('.env.example', '.'),
        ('config', 'config'),
        ('salesgpt_config', 'salesgpt_config'),
        ('blacklist_cache', 'blacklist_cache'),
        ('history', 'history'),
        ('logs', 'logs'),
        ('utils', 'utils'),
        ('services', 'services'),
        ('models', 'models'),
    ],
    hiddenimports=[
        'fastapi',
        'uvicorn',
        'uvicorn.lifespan',
        'uvicorn.lifespan.on',
        'uvicorn.protocols',
        'uvicorn.protocols.http',
        'uvicorn.protocols.websockets',
        'uvicorn.loops',
        'uvicorn.loops.auto',
        'uvicorn.logging',
        'pydantic',
        'pydantic.deprecated',
        'pydantic.deprecated.class_validators',
        'pydantic.deprecated.config',
        'pydantic.deprecated.copy_internals',
        'pydantic.deprecated.decorator',
        'pydantic.deprecated.json',
        'pydantic.deprecated.parse',
        'pydantic.deprecated.tools',
        'pydantic._internal',
        'pydantic._internal._validators',
        'pydantic._internal._fields',
        'pydantic._internal._model_construction',
        'pydantic._internal._typing_extra',
        'pydantic._internal._utils',
        'pydantic._internal._core_utils',
        'pydantic._internal._decorators',
        'pydantic._internal._generate_schema',
        'pydantic._internal._mock_val_ser',
        'pydantic._internal._std_types_schema',
        'pydantic.v1',
        'pydantic_core',
        'pydantic_core._pydantic_core',
        'sqlalchemy',
        'sqlalchemy.ext.asyncio',
        'aiosqlite',
        'httpx',
        'google.generativeai',
        'openai',
        'websockets',
        'python_multipart',
        'langchain',
        'langchain_core',
        'langchain_core.tools',
        'langchain_core.tools.base',
        'langchain_core._import_utils',
        'langchain_core.prompts',
        'langchain_core.output_parsers',
        'langchain_core.runnables',
        'langchain_community',
        'langchain_google_genai',
        'langchain_google_genai.chat_models',
        'pytz',
        'typing_extensions',
        'annotated_types',
        'email_validator',
        'multipart',
        'starlette',
        'starlette.applications',
        'starlette.middleware',
        'starlette.routing',
        'services.database',
        'services.protection_service',
        'services.ip_blacklist_service',
        'services.salesgpt_service',
        'services.customer_service',
        'services.form_service',
        'services.escalation_service',
        'services.memory_service',
        'services.conversation_rules_service',
        'services.customer_segmentation_service',
        'models.chat_models',
        'utils.file_logger',
        'utils.timezone_config',
        'utils.path_utils',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='YF_AI_Chat_Backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 显示控制台窗口以便查看日志
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open("backend.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ 配置文件创建完成: backend.spec")


def build_executable():
    """执行打包"""
    print("[INFO] 开始打包...")

    try:
        # 使用PyInstaller命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--clean",
            "--name", "YF_AI_Chat_Backend",
            "--icon", "resources/text_icon.ico",  # 添加图标
            "--additional-hooks-dir", ".",
            "--collect-all", "pydantic",
            "--collect-all", "pydantic_core",
            "--collect-all", "aiosqlite",
            "--collect-all", "sqlalchemy",
            "--collect-all", "langchain",
            "--collect-all", "langchain_core",
            "--collect-all", "langchain_google_genai",
            "--collect-all", "langchain_openai",
            "--collect-all", "openai",
            "--collect-all", "tiktoken",
            "--add-data", ".env.example;.",
            "--add-data", "config;config",
            "--add-data", "salesgpt_config;salesgpt_config",
            "--add-data", "blacklist_cache;blacklist_cache",
            "--add-data", "history;history",
            "--add-data", "logs;logs",
            "--add-data", "ssl;ssl",
            "--add-data", "utils;utils",
            "--add-data", "services;services",
            "--add-data", "models;models",
            "--hidden-import", "fastapi",
            "--hidden-import", "uvicorn",
            "--hidden-import", "uvicorn.lifespan.on",
            "--hidden-import", "pydantic",
            "--hidden-import", "sqlalchemy",
            "--hidden-import", "aiosqlite",
            "--hidden-import", "httpx",
            "--hidden-import", "google.generativeai",
            "--hidden-import", "openai",
            "--hidden-import", "websockets",
            "--hidden-import", "python_multipart",
            "--hidden-import", "langchain",
            "--hidden-import", "pytz",
            "--hidden-import", "services.database",
            "--hidden-import", "services.salesgpt_service",
            "--hidden-import", "models.chat_models",
            "--hidden-import", "utils.file_logger",
            "--hidden-import", "utils.timezone_config",
            "--hidden-import", "utils.path_utils",
            "main.py"
        ]

        print(f"[INFO] 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("[SUCCESS] 打包成功!")
            return True
        else:
            print(f"[ERROR] 打包失败: {result.stderr}")
            return False

    except subprocess.CalledProcessError as e:
        print(f"[ERROR] 打包过程中出现错误: {e}")
        print(f"[ERROR] 错误输出: {e.stderr}")
        return False

    except Exception as e:
        print(f"[ERROR] 打包过程中出现异常: {e}")
        return False



def create_portable_package():
    """创建便携版包"""
    print("[INFO] 创建便携版包...")

    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("[ERROR] dist目录不存在")
        return False

    exe_file = dist_dir / "YF_AI_Chat_Backend.exe"
    if not exe_file.exists():
        print("[ERROR] 可执行文件不存在")
        return False

    # 创建便携版目录
    portable_dir = dist_dir / "YF_AI_Chat_Backend_便携版"
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    
    portable_dir.mkdir()

    # 复制可执行文件
    shutil.copy2(exe_file, portable_dir / "YF_AI_Chat_Backend.exe")
    print(f"  已复制: YF_AI_Chat_Backend.exe")

    # 创建必要的目录结构
    directories = [
        "data",
        "config",
        "salesgpt_config",
        "blacklist_cache",
        "history",
        "logs",
        "ssl"
    ]

    for dir_name in directories:
        target_dir = portable_dir / dir_name
        target_dir.mkdir(exist_ok=True)
        
        # 如果源目录存在，复制其内容
        source_dir = Path(dir_name)
        if source_dir.exists():
            for item in source_dir.iterdir():
                if item.is_file():
                    # 跳过config目录中的.py文件
                    if dir_name == "config" and item.suffix == ".py":
                        print(f"    跳过Python文件: {item.name}")
                        continue
                    shutil.copy2(item, target_dir / item.name)
                elif item.is_dir():
                    shutil.copytree(item, target_dir / item.name, dirs_exist_ok=True)
        
        print(f"  已创建目录: {dir_name}")

    # 复制.env文件（如果存在）
    env_source = Path(".env")
    if env_source.exists():
        shutil.copy2(env_source, portable_dir / ".env")
        print(f"  已复制: .env")
    else:
        # 如果.env不存在，使用.env.example
        env_example = Path(".env.example")
        if env_example.exists():
            shutil.copy2(env_example, portable_dir / ".env")
            print(f"  已复制: .env.example -> .env")
        else:
            print(f"  警告: 未找到.env或.env.example文件")

    # 复制其他配置文件
    if Path("config").exists():
        print(f"  已复制config目录内容到便携版")
    if Path("salesgpt_config").exists():
        print(f"  已复制salesgpt_config目录内容到便携版")

    # 创建README文件
    readme_content = """# YF AI Chat Backend 便携版

## 使用说明

1. 确保已配置.env文件中的必要参数
2. 双击 YF_AI_Chat_Backend.exe 启动服务
3. 服务默认运行在 http://localhost:8000

## 目录说明

- data/          - 数据库文件存储目录
- config/        - 配置文件目录
- salesgpt_config/ - SalesGPT配置文件目录
- blacklist_cache/ - IP黑名单缓存目录
- history/       - 聊天记录导出目录
- logs/          - 日志文件目录
- .env           - 环境配置文件

## 注意事项

- 首次运行会自动创建数据库文件
- 请确保.env文件中的配置正确
- 日志文件会自动轮转，保留30天
"""

    readme_file = portable_dir / "README.md"
    with open(readme_file, "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("  已创建: README.md")
    print(f"✅ 便携版包创建完成: {portable_dir}")


def main():
    """主函数"""
    print("🚀 开始打包YF AI Chat Backend...")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("❌ 错误: 请在backend目录下运行此脚本")
        return False
    
    # 检查依赖
    if not check_requirements():
        return False
    
    # 清理之前的构建
    clean_build_dirs()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 创建便携版包
    create_portable_package()
    
    print("=" * 50)
    print("🎉 打包完成!")
    print(f"📁 输出目录: {Path('dist').absolute()}")
    print("📋 输出文件:")
    print("   - YF_AI_Chat_Backend.exe (单文件版)")
    print("   - YF_AI_Chat_Backend_便携版/ (便携版包)")
    
    return True


if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
