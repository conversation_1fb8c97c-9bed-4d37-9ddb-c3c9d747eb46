# YF AI Chat Backend 打包指南

## 概述

本指南将帮助您将YF AI Chat后端打包成独立的exe文件，实现以下功能：
- ✅ exe所在目录读取.env文件和配置文件
- ✅ 无论exe在哪都会寻找exe旁边的文件夹
- ✅ 自动创建必要的目录结构
- ✅ 包含所有依赖的便携版包

## 快速开始

### 方法1：使用批处理文件（推荐）

1. 双击运行 `打包后端.bat`
2. 等待打包完成
3. 在 `dist` 目录中找到打包结果

### 方法2：手动打包

1. 安装PyInstaller：
   ```bash
   pip install pyinstaller
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 运行打包脚本：
   ```bash
   python build.py
   ```

## 打包输出

打包完成后，会在 `dist` 目录生成以下文件：

### 1. 单文件版本
- `YF_AI_Chat_Backend.exe` - 独立可执行文件

### 2. 便携版包（推荐分发）
- `YF_AI_Chat_Backend_便携版/` 文件夹，包含：
  - `YF_AI_Chat_Backend.exe` - 可执行文件
  - `data/` - 数据库文件目录
  - `config/` - 配置文件目录
  - `salesgpt_config/` - SalesGPT配置目录
  - `blacklist_cache/` - IP黑名单缓存目录
  - `history/` - 聊天记录导出目录
  - `logs/` - 日志文件目录
  - `.env` - 环境配置文件
  - `README.md` - 使用说明

## 目录结构说明

打包后的目录结构：
```
YF_AI_Chat_Backend_便携版/
├── YF_AI_Chat_Backend.exe    # 主程序
├── .env                      # 环境配置文件
├── README.md                 # 使用说明
├── data/                     # 数据目录
│   └── chat_data.db         # 数据库文件（首次运行自动创建）
├── config/                   # 配置文件目录
│   ├── protection_rules.json
│   └── edit_protection_rules.py
├── salesgpt_config/          # SalesGPT配置目录
│   └── advanced_sales_config.json
├── blacklist_cache/          # IP黑名单缓存目录
│   ├── custom_blacklist.txt
│   ├── firehol_level1.txt
│   ├── ipsum.txt
│   └── spamhaus_drop.txt
├── history/                  # 聊天记录导出目录
│   └── README.md
└── logs/                     # 日志文件目录
    └── README.md
```

## 使用说明

### 1. 首次使用

1. 将 `YF_AI_Chat_Backend_便携版` 文件夹复制到目标位置
2. 编辑 `.env` 文件，配置必要的参数：
   - `GEMINI_API_KEY` - Gemini API密钥
   - `DATABASE_URL` - 数据库路径（自动设置为data/chat_data.db）
   - 其他配置参数

3. 双击 `YF_AI_Chat_Backend.exe` 启动服务

### 2. 配置说明

#### 重要配置项：
- **API密钥**：在.env文件中配置LLM提供商的API密钥
- **数据库**：自动使用data/chat_data.db，首次运行自动创建
- **端口**：默认8000，可在.env中修改PORT参数
- **代理**：如需代理，在.env中配置PROXY_*参数

#### 路径处理：
- 所有路径都相对于exe所在目录
- 配置文件、数据库、日志都在exe同级目录
- 支持在任意位置运行，无需安装

### 3. 运行和监控

- 启动后服务运行在 `http://localhost:8000`
- 日志文件保存在 `logs/` 目录
- 聊天记录导出到 `history/` 目录
- 数据库文件在 `data/` 目录

## 故障排除

### 常见问题

1. **启动失败**
   - 检查.env文件配置是否正确
   - 查看logs目录中的日志文件
   - 确保端口8000未被占用

2. **数据库错误**
   - 确保data目录有写入权限
   - 检查数据库文件是否损坏

3. **API调用失败**
   - 检查API密钥是否正确
   - 检查网络连接和代理设置

### 日志查看

日志文件位置：`logs/backend.log`
- 包含详细的运行日志
- 自动按天轮转
- 保留30天历史日志

## 技术说明

### 路径管理
- 使用 `utils/path_utils.py` 管理所有文件路径
- 自动检测运行环境（开发/打包）
- 确保在任意位置都能正确找到文件

### 依赖处理
- 所有Python依赖都打包到exe中
- 无需目标机器安装Python环境
- 包含所有必要的库文件

### 兼容性
- 支持Windows 10/11
- 64位系统
- 无需额外运行时环境

## 更新和维护

### 更新流程
1. 修改源代码
2. 重新运行打包脚本
3. 替换旧的exe文件
4. 保留配置文件和数据文件

### 备份建议
- 定期备份data目录（包含数据库）
- 备份.env配置文件
- 备份salesgpt_config目录

## 联系支持

如遇到问题，请检查：
1. 日志文件内容
2. 配置文件设置
3. 系统环境要求
