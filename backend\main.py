import os
import asyncio
import logging
from fastapi import FastAP<PERSON>, HTTPException, Depends, WebSocket, WebSocketDisconnect, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager
import json
from datetime import datetime
import uvicorn
from dotenv import load_dotenv

# 导入日志配置
from logging_config import setup_logging_filters, configure_uvicorn_logging

# 导入电源管理
from power_management import start_power_protection, stop_power_protection, get_power_status

# 导入EXE性能包装器
try:
    from exe_performance_wrapper import setup_performance_optimization, keep_system_awake
    EXE_PERFORMANCE_AVAILABLE = True
except ImportError:
    EXE_PERFORMANCE_AVAILABLE = False

from services.database import DatabaseService
from services.protection_service import protection_service
from services.ip_blacklist_service import ip_blacklist_service
# SalesGPT service will be imported when needed
from services.salesgpt_service import get_salesgpt_service
from models.chat_models import (
    ChatMessage,
    ChatSession,
    UserMessage,
    FormSubmission,
    FormSubmissionResponse,
    FormSubmissionUpdateRequest
)
from utils.file_logger import backend_logger as logger
from utils.timezone_config import parse_iso_time, format_time_for_display, get_timezone_info

class ConnectionErrorMiddleware(BaseHTTPMiddleware):
    """处理连接错误的中间件"""

    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except ConnectionResetError:
            # 静默处理连接重置错误
            logger.debug(f"连接被客户端重置: {request.client.host if request.client else 'unknown'}")
            return JSONResponse(
                status_code=499,  # Client Closed Request
                content={"detail": "Connection reset by client"}
            )
        except Exception as e:
            # 记录其他异常但不暴露详细信息
            logger.error(f"请求处理异常: {type(e).__name__}: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error"}
            )

# Load environment variables
from utils.path_utils import get_env_file_path, copy_default_files_if_needed

# 确保必要的文件和目录存在
copy_default_files_if_needed()

# 加载环境变量文件
env_file = get_env_file_path()
load_dotenv(dotenv_path=env_file)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.startup("🚀 YF AI Chat Backend 启动中...")

    # 检查是否为打包的EXE环境
    is_exe = getattr(sys, 'frozen', False)
    if is_exe and EXE_PERFORMANCE_AVAILABLE:
        logger.startup("📦 检测到EXE环境，应用高性能优化...")
        setup_performance_optimization()
        keep_system_awake()
        logger.success("✅ EXE高性能优化已启用")

    # 启动Windows电源保护
    logger.startup("🔋 启动Windows电源管理保护...")
    power_started = start_power_protection()
    if power_started:
        power_status = get_power_status()
        logger.success(f"✅ 电源保护已启动 - 当前电源计划: {power_status['current_power_plan']}")
    else:
        logger.info("ℹ️ 电源保护跳过（非Windows系统）")

    logger.startup("正在初始化数据库...")
    await db_service.init_db()
    logger.success("数据库初始化完成")

    # 启动后台任务
    logger.startup("启动管理员超时检查任务...")
    timeout_task = asyncio.create_task(check_admin_timeout_task())

    # 显示配置信息
    timeout_minutes = int(os.getenv("ADMIN_CONTROL_TIMEOUT_MINUTES", 15))
    check_interval_minutes = int(os.getenv("ADMIN_TIMEOUT_CHECK_INTERVAL_MINUTES", 1))
    logger.startup(f"管理员超时配置 - 超时时间: {timeout_minutes}分钟, 检查间隔: {check_interval_minutes}分钟")

    yield

    # 关闭时执行
    logger.shutdown("正在清理资源...")

    # 停止电源保护
    logger.shutdown("🔋 停止Windows电源管理保护...")
    stop_power_protection()

    timeout_task.cancel()
    try:
        await timeout_task
    except asyncio.CancelledError:
        pass
    logger.success("应用关闭完成")

app = FastAPI(
    title="YF AI Chat Backend",
    version="0.0.1",
    lifespan=lifespan
)

# 添加连接错误处理中间件
app.add_middleware(ConnectionErrorMiddleware)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Token管理 - 支持多种类型的token
TOKENS = {
    "wordpress": os.getenv("WORDPRESS_TOKEN", "wp_default_token"),
    "admin": os.getenv("ADMIN_TOKEN", "admin_default_token"),
    "api": os.getenv("API_TOKEN", "api_default_token")  # 保持向后兼容
}

# Token权限映射
TOKEN_PERMISSIONS = {
    "wordpress": ["chat"],  # WordPress插件只能访问聊天API
    "admin": ["chat", "admin", "websocket"],  # 管理员可以访问所有功能
    "api": ["chat", "admin", "websocket"]  # API token保持完整权限（向后兼容）
}

# Services
db_service = DatabaseService()

def generate_order_submission_confirmation(order_detection: Dict[str, Any]) -> str:
    """生成订单提交确认和邮件等待通知"""
    order_info = order_detection.get("order_info", {}) if isinstance(order_detection, dict) else order_detection

    order_number = order_info.get("order_number", "your order")
    platform = order_info.get("platform", "the platform")
    issue_type = order_info.get("issue_type", "your concern")

    platform_display = {
        "amazon": "Amazon",
        "ebay": "eBay",
        "walmart": "Walmart",
        "shopify": "our official website",
        "unknown": "the specified platform"
    }.get(platform, platform)

    issue_display = {
        "defective": "product issue",
        "warranty": "warranty concern",
        "return": "return/exchange request",
        "delivery": "delivery problem",
        "technical": "technical issue",
        "general": "inquiry"
    }.get(issue_type, "concern")

    return f"""✅ **Thank you!** I've successfully submitted your information to our specialized support team.

📋 **Submission Details:**
• **Platform:** {platform_display}
• **Order:** {order_number}
• **Issue Type:** {issue_display}
• **Priority:** High (order-related)

📧 **What happens next:**
1. Our support team will review your case within **2-4 hours**
2. You'll receive a detailed response via **email notification**
3. The email will include specific solutions and next steps
4. If needed, our team will coordinate any returns, exchanges, or repairs

⏰ **Expected Response Time:** Within 24 hours (usually much faster)

Is there anything else I can help you with while you wait? I'm here to assist with any other questions about our products or services."""

# WebSocket connections for admin monitoring
admin_connections: Dict[str, WebSocket] = {}

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.admin_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, connection_type: str = "user", admin_id: str = None):
        await websocket.accept()
        if connection_type == "admin" and admin_id:
            self.admin_connections[admin_id] = websocket
        else:
            self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket, connection_type: str = "user", admin_id: str = None):
        if connection_type == "admin" and admin_id and admin_id in self.admin_connections:
            del self.admin_connections[admin_id]
        elif websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast_to_admins(self, message: str):
        for connection in self.admin_connections.values():
            await connection.send_text(message)

manager = ConnectionManager()

# 后台任务：定期检查超时的管理员控制
async def check_admin_timeout_task():
    """定期检查并释放超时的管理员控制"""
    # 从环境变量读取配置
    timeout_minutes = int(os.getenv("ADMIN_CONTROL_TIMEOUT_MINUTES", 15))
    check_interval_minutes = int(os.getenv("ADMIN_TIMEOUT_CHECK_INTERVAL_MINUTES", 1))
    check_interval_seconds = check_interval_minutes * 60

    logger.debug(f"管理员超时检查任务启动 - 超时时间: {timeout_minutes}分钟, 检查间隔: {check_interval_minutes}分钟")

    while True:
        try:
            # 根据配置的间隔时间检查
            await asyncio.sleep(check_interval_seconds)

            # 检查并释放超时的会话
            released_sessions = await db_service.check_and_release_expired_admin_control(timeout_minutes)

            if released_sessions:
                logger.debug(f"释放了 {len(released_sessions)} 个超时的管理员控制会话 (超时时间: {timeout_minutes}分钟)")

                # 通知管理员客户端状态变化
                for session_info in released_sessions:
                    await manager.broadcast_to_admins(json.dumps({
                        "type": "admin_timeout",
                        "session_id": session_info["session_id"],
                        "admin_id": session_info["admin_id"],
                        "message": "管理员控制已超时自动释放"
                    }, default=str))

        except Exception as e:
            logger.error(f"检查管理员超时任务出错: {e}")

def get_client_ip(request: Request) -> str:
    """获取客户端真实IP地址"""
    # 检查代理头部
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For可能包含多个IP，取第一个
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()

    # 回退到直接连接IP
    return request.client.host if request.client else "unknown"

def verify_token(required_permission: str = "chat"):
    """创建token验证依赖，支持权限检查"""
    def _verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
        token = credentials.credentials


        # 检查token是否有效
        token_type = None
        for t_type, t_value in TOKENS.items():
            if token == t_value:
                token_type = t_type
                break

        if not token_type:

            raise HTTPException(status_code=401, detail="Invalid token")

        # 检查权限
        if required_permission not in TOKEN_PERMISSIONS.get(token_type, []):

            raise HTTPException(status_code=403, detail="Insufficient permissions")


        return {"token": token, "type": token_type}

    return _verify_token

@app.get("/")
async def root():
    return {"message": "YF AI Chat Backend", "version": "0.0.1"}

@app.get("/api/timezone")
async def get_timezone_info_endpoint():
    """获取服务器时区信息"""
    return get_timezone_info()

@app.get("/api/admin/protection/stats")
async def get_protection_stats(auth: dict = Depends(verify_token("admin"))):
    """获取防护系统统计信息"""
    return protection_service.get_stats()

@app.get("/api/admin/protection/config")
async def get_protection_config(auth: dict = Depends(verify_token("admin"))):
    """获取防护系统配置"""
    protection_service.load_config()  # 强制重新加载
    return protection_service.config

@app.post("/api/admin/salesgpt/reload")
async def reload_salesgpt_config(auth: dict = Depends(verify_token("admin"))):
    """手动重新加载SalesGPT配置文件"""
    try:
        from services.salesgpt_service import get_salesgpt_service
        from services.conversation_rules_service import conversation_rules_service
        from services.escalation_service import escalation_service

        salesgpt_service = get_salesgpt_service()
        salesgpt_service.load_sales_config()

        # 重新加载对话规则和升级规则
        conversation_rules_service.load_config()
        escalation_service.load_config()

        return {
            "success": True,
            "message": "SalesGPT configuration reloaded successfully",
            "timestamp": datetime.now().isoformat(),
            "configs_loaded": {
                "agent_profile": bool(salesgpt_service.sales_config.get('agent_profile')),
                "sales_process": bool(salesgpt_service.sales_config.get('sales_process')),
                "purchase_channels": bool(salesgpt_service.sales_config.get('purchase_channels')),
                "after_sales_support": bool(salesgpt_service.sales_config.get('after_sales_support')),
                "conversation_rules": bool(conversation_rules_service.config),
                "escalation_rules": bool(escalation_service.config)
            }
        }
    except Exception as e:
        logger.error(f"重新加载SalesGPT配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reload SalesGPT configuration: {str(e)}")

@app.post("/api/admin/escalate/{session_id}")
async def manual_escalate_session(
    session_id: str,
    reason: str = "manual_request",
    auth: dict = Depends(verify_token("admin"))
):
    """手动升级会话到人工客服"""
    try:
        from services.salesgpt_service import get_salesgpt_service
        salesgpt_service = get_salesgpt_service()

        # 获取对话历史
        messages = await db_service.get_session_messages(session_id)
        conversation_history = [
            {
                "role": "user" if msg.sender == "user" else "assistant",
                "content": msg.content,
                "timestamp": msg.created_at.isoformat()
            }
            for msg in messages
        ]

        # 执行手动升级
        handover_data = salesgpt_service.manual_escalation(session_id, reason, conversation_history)

        if "error" in handover_data:
            raise HTTPException(status_code=500, detail=handover_data["error"])

        # 通知管理员
        await manager.broadcast_to_admins(json.dumps({
            "type": "manual_escalation",
            "session_id": session_id,
            "reason": reason,
            "handover_data": handover_data
        }, default=str))

        return {
            "success": True,
            "message": "Session escalated successfully",
            "handover_data": handover_data
        }

    except Exception as e:
        logger.error(f"手动升级失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to escalate session: {str(e)}")

@app.get("/api/admin/salesgpt/config")
async def get_salesgpt_config(auth: dict = Depends(verify_token("admin"))):
    """获取SalesGPT配置信息"""
    try:
        from services.salesgpt_service import get_salesgpt_service
        salesgpt_service = get_salesgpt_service()

        return {
            "agent_profile": salesgpt_service.sales_config.get('agent_profile', {}),
            "sales_process_stages": list(salesgpt_service.sales_config.get('sales_process', {}).get('stages', {}).keys()),
            "purchase_channels": list(salesgpt_service.sales_config.get('purchase_channels', {}).keys()),
            "after_sales_enabled": salesgpt_service.sales_config.get('after_sales_support', {}).get('basic_support', {}).get('enabled', False),
            "form_collection_enabled": salesgpt_service.sales_config.get('after_sales_support', {}).get('advanced_support', {}).get('enabled', False),
            "conversation_rules": salesgpt_service.get_conversation_rules_status(),
            "escalation_rules": salesgpt_service.get_escalation_rules_status(),
            "last_checked": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取SalesGPT配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get SalesGPT configuration: {str(e)}")

@app.get("/api/admin/blacklist/stats")
async def get_blacklist_stats(auth: dict = Depends(verify_token("admin"))):
    """获取IP黑名单统计信息"""
    try:
        stats = ip_blacklist_service.get_stats()
        return {
            "success": True,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取黑名单统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get blacklist stats: {str(e)}")

@app.post("/api/admin/blacklist/update")
async def update_blacklist(auth: dict = Depends(verify_token("admin"))):
    """手动更新IP黑名单"""
    try:
        result = await ip_blacklist_service.force_update()
        return result
    except Exception as e:
        logger.error(f"更新黑名单失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update blacklist: {str(e)}")

@app.post("/api/admin/blacklist/test")
async def test_ip_blacklist(
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """测试IP是否在黑名单中"""
    ip = request.get("ip")
    if not ip:
        raise HTTPException(status_code=400, detail="IP address is required")

    try:
        result = await ip_blacklist_service.test_ip(ip)
        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"测试IP黑名单失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to test IP: {str(e)}")

@app.post("/api/admin/blacklist/add")
async def add_ip_to_blacklist(
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """手动添加IP到黑名单"""
    ip = request.get("ip")
    reason = request.get("reason", "manual_admin_add")

    if not ip:
        raise HTTPException(status_code=400, detail="IP address is required")

    try:
        success = ip_blacklist_service.add_custom_ip(ip, reason)
        if success:
            return {
                "success": True,
                "message": f"IP {ip} added to blacklist",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="Invalid IP address")
    except Exception as e:
        logger.error(f"添加IP到黑名单失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to add IP to blacklist: {str(e)}")

@app.post("/api/admin/blacklist/remove")
async def remove_ip_from_blacklist(
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """从黑名单中移除IP"""
    ip = request.get("ip")

    if not ip:
        raise HTTPException(status_code=400, detail="IP address is required")

    try:
        success = ip_blacklist_service.remove_custom_ip(ip)
        if success:
            return {
                "success": True,
                "message": f"IP {ip} removed from blacklist",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "message": f"IP {ip} not found in blacklist",
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        logger.error(f"从黑名单移除IP失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to remove IP from blacklist: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查端点 - 不需要认证"""
    logger.info("健康检查请求")
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "service": "YF AI Chat Backend"
    }

@app.get("/api/power-status")
async def get_power_status_endpoint(auth: dict = Depends(verify_token("admin"))):
    """获取Windows电源管理状态 - 需要管理员权限"""
    try:
        power_status = get_power_status()
        return {
            "success": True,
            "data": power_status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取电源状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get power status: {str(e)}")

@app.post("/api/export-session")
async def export_session_endpoint(
    request: dict,
    auth: dict = Depends(verify_token("chat"))
):
    """导出会话记录到CSV"""
    try:
        session_id = request.get("session_id")
        reason = request.get("reason", "user_export")

        if not session_id:
            raise HTTPException(status_code=400, detail="Session ID is required")

        # 获取SalesGPT服务实例
        salesgpt = get_salesgpt_service(db_service)

        # 导出会话记录
        filepath = await salesgpt.export_session_to_csv(session_id, reason)

        if filepath:
            # 🔧 导出成功后，清空数据库中该session的所有记录
            await salesgpt.clear_session_from_database(session_id)

            return {
                "success": True,
                "message": "Session exported and cleared successfully",
                "filepath": os.path.basename(filepath)
            }
        else:
            return {
                "success": False,
                "message": "Failed to export session"
            }

    except Exception as e:
        logger.error(f"Export session error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/chat")
async def chat_endpoint(
    message: UserMessage,
    request: Request,
    auth: dict = Depends(verify_token("chat"))
):
    start_time = datetime.now()  # 🔍 记录开始时间
    try:
        # 获取客户端IP
        client_ip = get_client_ip(request)

        # 🔍 添加详细的请求日志
        logger.info(f"🚀 收到聊天请求 - IP: {client_ip}, Session: {message.session_id}, Message: {message.content[:50]}...")
        logger.debug(f"🔍 完整请求内容: {message.model_dump()}")
        logger.debug(f"🔍 请求头: {dict(request.headers)}")

        # 🛡️ 防护检查
        protection_passed, protection_error = await protection_service.check_message_protection(
            message.content, client_ip, message.session_id
        )

        if not protection_passed:
            logger.warning(f"消息被防护系统阻止: {client_ip} - {protection_error}")
            raise HTTPException(status_code=429, detail=protection_error)

        # 记录用户消息（包含IP信息）
        logger.chat_user(message.session_id, message.content, client_ip)

        # Save user message
        session = await db_service.get_or_create_session(message.session_id)
        logger.database_operation("会话获取", "chat_sessions", 1)

        user_msg = await db_service.save_message(
            session_id=message.session_id,
            content=message.content,
            sender="user"
        )
        logger.database_operation("用户消息保存", "chat_messages", 1)

        # Broadcast to admins
        await manager.broadcast_to_admins(json.dumps({
            "type": "new_message",
            "session_id": message.session_id,
            "message": user_msg.dict()
        }, default=str))
        logger.websocket_event("新消息通知", message.session_id, f"{len(manager.admin_connections)} 个管理员连接")

        # Check if session is under admin control
        logger.debug(f"🔍 检查会话控制状态: admin_control={session.admin_control}")
        if session.admin_control:
            logger.debug("会话由管理员控制，等待人工回复")
            # 不返回任何消息，让前端等待人工回复
            return {
                "response": "",
                "session_id": message.session_id,
                "admin_controlled": True,
                "user_message_id": user_msg.id,  # 🔧 仍然返回用户消息ID
                "user_message_data": user_msg.dict()
            }

        # 🚀 Process with SalesGPT
        logger.debug("正在处理SalesGPT服务...")
        try:
            from services.salesgpt_service import get_salesgpt_service
            # 传递数据库服务实例，避免重复初始化
            salesgpt_service = get_salesgpt_service(db_service)
            logger.debug(f"SalesGPT服务实例获取成功: {type(salesgpt_service)}")

            salesgpt_response = await salesgpt_service.process_message(
                session_id=message.session_id,
                message=message.content,
                user_ip=client_ip
            )
            logger.debug(f"SalesGPT处理完成: {salesgpt_response}")
        except Exception as e:
            logger.error(f"SalesGPT服务调用失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            # 返回错误响应
            error_response = "I apologize, but I'm experiencing technical difficulties. Please try again later."
            ai_msg = await db_service.save_message(
                session_id=message.session_id,
                content=error_response,
                sender="ai"
            )
            logger.chat_ai(message.session_id, error_response)
            return {
                "response": error_response,
                "session_id": message.session_id,
                "ai_message_id": ai_msg.id,
                "ai_message_data": ai_msg.dict(),
                "user_message_id": user_msg.id,
                "user_message_data": user_msg.dict(),
                "error_fallback": True
            }

        # 🎯 用户偏好学习现在由SalesGPT的memory_service处理

        # 🔍 检查SalesGPT是否触发了人工客服接管
        if salesgpt_response.get("status") == "handover":
            logger.info(f"SalesGPT触发人工客服接管: {salesgpt_response.get('handover_id')}")

            # 设置会话为管理员控制状态，等待人工接管
            await db_service.set_admin_control(message.session_id, True, "system_handover")

            # 通知管理员有新的接管请求
            await manager.broadcast_to_admins(json.dumps({
                "type": "handover_request",
                "session_id": message.session_id,
                "user_message": message.content,
                "handover_reason": "SalesGPT automatic handover",
                "timestamp": datetime.now().isoformat(),
                "priority": "high"
            }, default=str))

            return {
                "response": salesgpt_response.get('response'),
                "session_id": message.session_id,
                "user_message_id": user_msg.id,
                "user_message_data": user_msg.dict(),
                "handover_requested": True,
                "status": "handover"
            }

        # 🔍 检查是否有错误
        if salesgpt_response.get("status") == "error":
            logger.error(f"SalesGPT处理错误: {salesgpt_response.get('error')}")
            # 返回错误响应
            error_response = "I apologize, but I'm experiencing technical difficulties. Please try again later."

            # 保存错误回复
            ai_msg = await db_service.save_message(
                session_id=message.session_id,
                content=error_response,
                sender="ai"
            )

            logger.chat_ai(message.session_id, error_response)

            return {
                "response": error_response,
                "session_id": message.session_id,
                "ai_message_id": ai_msg.id,
                "ai_message_data": ai_msg.dict(),
                "user_message_id": user_msg.id,
                "user_message_data": user_msg.dict(),
                "error_fallback": True
            }

        # 🎯 正常的SalesGPT响应处理
        logger.debug("处理SalesGPT正常响应...")

        # SalesGPT已经处理了消息并保存到数据库，直接使用响应
        response_text = salesgpt_response.get('response', '')

        # 记录AI回复到终端日志
        logger.chat_ai(message.session_id, response_text)

        # 获取最新保存的AI消息（SalesGPT已经保存了）
        # 查询最新的AI消息 - 增加limit以确保能找到AI消息
        session_messages = await db_service.get_session_messages(message.session_id, limit=10)
        ai_msg = None
        for msg in reversed(session_messages):
            if msg.sender == "ai":
                ai_msg = msg
                break

        # 只在特定情况下推送给管理员
        salesgpt_status = salesgpt_response.get('status', '')
        should_notify_admin = salesgpt_status in ['form_collection', 'form_initiated', 'form_completed', 'handover', 'handover_fallback']

        if should_notify_admin:
            # 推送给管理员（表单相关或需要人工介入）
            await manager.broadcast_to_admins(json.dumps({
                "type": "customer_service_request",
                "session_id": message.session_id,
                "status": salesgpt_status,
                "message": {
                    "content": response_text,
                    "sender": "ai",
                    "timestamp": salesgpt_response.get('timestamp')
                }
            }, default=str))
            logger.websocket_event("客服请求通知", "管理员")
        else:
            # 普通AI回复，不推送给管理员
            logger.debug("普通AI回复，不推送给管理员")

        response_data = {
            "response": response_text,
            "session_id": message.session_id,
            "user_message_id": user_msg.id,
            "user_message_data": user_msg.dict(),
            "salesgpt_processed": True
        }

        # 如果找到了AI消息，添加AI消息信息
        if ai_msg:
            response_data["ai_message_id"] = ai_msg.id
            response_data["ai_message_data"] = ai_msg.dict()

        logger.debug(f"SalesGPT聊天处理完成: {message.session_id}")

        return response_data

    except Exception as e:
        import traceback
        error_msg = f"聊天处理失败: {type(e).__name__}: {str(e)}"
        logger.error(error_msg)
        logger.error(f"错误堆栈: {traceback.format_exc()}")

        # 确保错误信息不为空
        detail = str(e) if str(e) else f"Internal server error: {type(e).__name__}"
        raise HTTPException(status_code=500, detail=detail)

@app.websocket("/ws/admin/{admin_id}")
async def websocket_admin_endpoint(websocket: WebSocket, admin_id: str):
    await manager.connect(websocket, "admin", admin_id)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)

            if message_data["type"] == "admin_message":
                # Admin sending message to user
                session_id = message_data["session_id"]
                content = message_data["content"]

                # Save admin message
                await db_service.save_message(
                    session_id=session_id,
                    content=content,
                    sender="admin",
                    admin_id=admin_id
                )

                # Set session under admin control
                await db_service.set_admin_control(session_id, True, admin_id)

            elif message_data["type"] == "release_control":
                # Admin releasing control back to AI
                session_id = message_data["session_id"]
                await db_service.set_admin_control(session_id, False)

    except WebSocketDisconnect:
        logger.debug(f"管理员 {admin_id} WebSocket连接正常断开")
        manager.disconnect(websocket, "admin", admin_id)
    except ConnectionResetError:
        logger.debug(f"管理员 {admin_id} 连接被重置")
        manager.disconnect(websocket, "admin", admin_id)
    except Exception as e:
        logger.warning(f"管理员 {admin_id} WebSocket连接异常: {type(e).__name__}: {str(e)}")
        manager.disconnect(websocket, "admin", admin_id)

@app.get("/api/admin/sessions")
async def get_sessions(
    admin_id: str = None,
    include_archived: bool = False,
    auth: dict = Depends(verify_token("admin"))
):
    if admin_id:
        # 返回包含该管理员已读状态的会话列表
        sessions = await db_service.get_all_sessions_with_read_status(admin_id, include_archived=include_archived)
    else:
        # 返回基本会话列表（向后兼容）
        sessions = await db_service.get_all_sessions(include_archived=include_archived)
    return {"sessions": sessions}

@app.get("/api/admin/session/{session_id}/messages")
async def get_session_messages(session_id: str, auth: dict = Depends(verify_token("admin"))):
    messages = await db_service.get_session_messages(session_id)
    return {"messages": messages}

@app.get("/api/session/{session_id}/exists")
async def check_session_exists(
    session_id: str,
    auth: dict = Depends(verify_token("chat"))
):
    """检查会话是否存在于数据库中"""
    try:
        # 检查会话是否存在
        session = await db_service.get_session(session_id)
        session_exists = session is not None

        # 如果会话存在，获取消息数量
        message_count = 0
        if session_exists:
            messages = await db_service.get_session_messages(session_id, limit=1000)
            message_count = len(messages)

        logger.debug(f"会话存在性检查: {session_id} - 存在: {session_exists}, 消息数: {message_count}")

        return {
            "exists": session_exists,
            "message_count": message_count
        }

    except Exception as e:
        logger.error(f"检查会话存在性失败: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/api/session/{session_id}/messages")
async def get_session_messages_for_user(
    session_id: str,
    since: str = None,
    auth: dict = Depends(verify_token("chat"))
):
    """获取会话消息（供WordPress前端使用）"""
    try:
        # 判断是否为轮询请求（通常轮询请求会带since参数或者频繁请求）
        is_polling = since is not None

        if is_polling:
            # 轮询请求只记录到文件，不在终端显示
            logger.debug(f"轮询请求: {session_id}, since: {since}")
        else:
            logger.debug(f"前端请求会话消息: {session_id}, since: {since}")

        # 首先检查会话是否存在
        session = await db_service.get_session(session_id)
        if not session:
            logger.warning(f"会话不存在: {session_id}")
            raise HTTPException(status_code=404, detail="Session not found")

        # 获取会话消息
        messages = await db_service.get_session_messages(session_id)

        # 将Pydantic模型转换为字典 (Pydantic v1 使用 dict() 方法)
        message_dicts = [msg.dict() for msg in messages]

        # 如果指定了since参数，只返回该时间之后的消息
        if since:
            try:
                # 解析since参数，处理各种可能的格式
                if since.endswith('Z'):
                    # 移除毫秒部分，只保留到秒
                    since_clean = since.replace('.000Z', '').replace('Z', '')
                    since_datetime = datetime.fromisoformat(since_clean)
                elif '+' in since or since.count('-') > 2:  # 包含时区信息
                    since_datetime = datetime.fromisoformat(since)
                    if since_datetime.tzinfo is not None:
                        since_datetime = since_datetime.replace(tzinfo=None)
                else:
                    # 没有时区信息，直接解析
                    since_datetime = datetime.fromisoformat(since)

                # 为了安全比较，将since_datetime转换为naive datetime（移除时区信息）
                since_naive = since_datetime.replace(tzinfo=None)

                filtered_messages = []
                for msg in message_dicts:
                    # 处理created_at字段，可能是字符串或datetime对象
                    created_at = msg['created_at']
                    if isinstance(created_at, str):
                        # 解析字符串格式的时间
                        if created_at.endswith('Z'):
                            msg_datetime = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                            msg_naive = msg_datetime.replace(tzinfo=None)
                        elif '+' in created_at or created_at.count('-') > 2:
                            msg_datetime = datetime.fromisoformat(created_at)
                            msg_naive = msg_datetime.replace(tzinfo=None)
                        else:
                            msg_naive = datetime.fromisoformat(created_at)
                    else:
                        # 如果是datetime对象，确保是naive datetime
                        if hasattr(created_at, 'tzinfo') and created_at.tzinfo is not None:
                            msg_naive = created_at.replace(tzinfo=None)
                        else:
                            msg_naive = created_at

                    # 使用naive datetime进行比较（使用>而不是>=，避免重复返回相同消息）
                    if msg_naive > since_naive:
                        filtered_messages.append(msg)

                message_dicts = filtered_messages
                if not is_polling:
                    logger.debug(f"筛选后消息数量: {len(message_dicts)}")
            except (ValueError, TypeError) as e:
                logger.warning(f"处理since参数时出错: {since}, 错误: {e}")
                # 如果解析失败，返回所有消息而不是报错
                if not is_polling:
                    logger.debug("since参数解析失败，返回所有消息")

        if not is_polling:
            logger.debug(f"返回消息数量: {len(message_dicts)}")
        return {"messages": message_dicts}

    except HTTPException:
        # 重新抛出HTTPException（如404）
        raise
    except Exception as e:
        logger.error(f"获取消息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/admin/session/{session_id}/message")
async def send_admin_message(
    session_id: str,
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """发送管理员消息"""
    message = request.get("message")
    admin_id = request.get("admin_id")



    if not message:
        raise HTTPException(status_code=400, detail="message is required")
    if not admin_id:
        raise HTTPException(status_code=400, detail="admin_id is required")

    try:

        # 记录管理员消息
        logger.chat_admin(session_id, message, admin_id)

        # 保存管理员消息
        admin_msg = await db_service.save_message(
            session_id=session_id,
            content=message,
            sender="admin",
            admin_id=admin_id
        )
        logger.database_operation("管理员消息保存", "chat_messages", 1)

        # 通知其他管理员客户端（包含完整的消息数据和ID）
        await manager.broadcast_to_admins(json.dumps({
            "type": "admin_message",
            "session_id": session_id,
            "message": admin_msg.dict()
        }, default=str))
        logger.websocket_event("通知其他管理员", f"{len(manager.admin_connections)} 个连接")

        # 设置会话为管理员控制状态（重要：这样前端用户就知道有管理员介入了）
        await db_service.set_admin_control(session_id, True, admin_id)
        logger.debug("会话已设置为管理员控制状态")

        # 返回完整的消息数据，包括数据库生成的ID
        return {
            "success": True,
            "message": "Message sent successfully",
            "message_id": admin_msg.id,
            "message_data": admin_msg.dict()  # 包含完整的消息数据
        }
    except Exception as e:
        logger.error(f"发送管理员消息失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to send admin message")

@app.post("/api/admin/session/{session_id}/mark_read")
async def mark_session_read(
    session_id: str,
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """标记会话为已读"""
    admin_id = request.get("admin_id")
    if not admin_id:
        raise HTTPException(status_code=400, detail="admin_id is required")

    try:
        # 调用数据库服务来标记已读
        await db_service.mark_session_read(session_id, admin_id)
        logger.debug(f"会话 {session_id} 被管理员 {admin_id} 标记为已读")
        return {"success": True, "message": "Session marked as read"}
    except Exception as e:
        logger.error(f"标记会话已读失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to mark session as read")

@app.post("/api/admin/session/{session_id}/take")
async def take_session_control(
    session_id: str,
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """接管会话控制"""
    admin_id = request.get("admin_id")
    if not admin_id:
        raise HTTPException(status_code=400, detail="admin_id is required")

    try:
        # 设置会话为管理员控制
        await db_service.set_admin_control(session_id, True, admin_id)
        logger.admin_action(admin_id, "接管会话", session_id)
        return {"success": True, "message": "Session control taken"}
    except Exception as e:
        logger.error(f"接管会话控制失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to take session control")

@app.post("/api/admin/session/{session_id}/release")
async def release_session_control(
    session_id: str,
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """释放会话控制"""
    admin_id = request.get("admin_id")
    if not admin_id:
        raise HTTPException(status_code=400, detail="admin_id is required")

    try:
        # 释放会话控制，回到AI模式
        await db_service.set_admin_control(session_id, False)
        logger.admin_action(admin_id, "释放会话", session_id)
        return {"success": True, "message": "Session control released"}
    except Exception as e:
        logger.error(f"释放会话控制失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to release session control")

@app.get("/api/admin/session/{session_id}/status")
async def get_session_status(
    session_id: str,
    auth: dict = Depends(verify_token("admin"))
):
    """获取会话的实时状态"""
    try:
        session = await db_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        return {
            "session_id": session_id,
            "admin_control": session.admin_control,
            "admin_control_at": session.admin_control_at.isoformat() if session.admin_control_at else None,
            "admin_control_by": session.admin_control_by,
            "updated_at": session.updated_at.isoformat()
        }
    except Exception as e:
        logger.error(f"获取会话状态失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session status")

@app.post("/api/admin/session/{session_id}/archive")
async def archive_session(
    session_id: str,
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """归档会话"""
    admin_id = request.get("admin_id")
    if not admin_id:
        raise HTTPException(status_code=400, detail="admin_id is required")

    try:
        # 检查会话是否存在
        session = await db_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # 归档会话
        await db_service.archive_session(session_id, admin_id)
        logger.admin_action(admin_id, "归档会话", session_id)
        return {"success": True, "message": "Session archived successfully"}
    except Exception as e:
        logger.error(f"归档会话失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to archive session")

@app.post("/api/admin/session/{session_id}/unarchive")
async def unarchive_session(
    session_id: str,
    auth: dict = Depends(verify_token("admin"))
):
    """取消归档会话"""
    try:
        # 检查会话是否存在
        session = await db_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # 取消归档
        await db_service.unarchive_session(session_id)
        logger.info(f"会话 {session_id} 已取消归档")
        return {"success": True, "message": "Session unarchived successfully"}
    except Exception as e:
        logger.error(f"取消归档会话失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to unarchive session")

@app.get("/api/admin/archived-sessions")
async def get_archived_sessions(
    admin_id: str = None,
    auth: dict = Depends(verify_token("admin"))
):
    """获取归档的会话列表"""
    try:
        sessions = await db_service.get_archived_sessions(admin_id)
        return {"sessions": sessions}
    except Exception as e:
        logger.error(f"获取归档会话失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to get archived sessions")

@app.post("/api/admin/check_timeouts")
async def check_admin_timeouts(auth: dict = Depends(verify_token("admin"))):
    """手动检查并释放超时的管理员控制"""
    try:
        # 从环境变量读取超时时间
        timeout_minutes = int(os.getenv("ADMIN_CONTROL_TIMEOUT_MINUTES", 15))
        released_sessions = await db_service.check_and_release_expired_admin_control(timeout_minutes)

        # 通知管理员客户端状态变化
        for session_info in released_sessions:
            await manager.broadcast_to_admins(json.dumps({
                "type": "admin_timeout",
                "session_id": session_info["session_id"],
                "admin_id": session_info["admin_id"],
                "message": "管理员控制已超时自动释放"
            }, default=str))

        return {
            "success": True,
            "released_count": len(released_sessions),
            "released_sessions": released_sessions,
            "timeout_minutes": timeout_minutes
        }
    except Exception as e:
        logger.error(f"检查超时失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to check timeouts")

@app.get("/api/admin/config")
async def get_admin_config(auth: dict = Depends(verify_token("admin"))):
    """获取管理员配置信息"""
    try:
        timeout_minutes = int(os.getenv("ADMIN_CONTROL_TIMEOUT_MINUTES", 15))
        check_interval_minutes = int(os.getenv("ADMIN_TIMEOUT_CHECK_INTERVAL_MINUTES", 1))

        return {
            "admin_control_timeout_minutes": timeout_minutes,
            "admin_timeout_check_interval_minutes": check_interval_minutes,
            "timezone": os.getenv("DEFAULT_TIMEZONE", "America/Los_Angeles"),
            "log_level": os.getenv("LOG_LEVEL", "simple")
        }
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to get admin config")

@app.get("/api/admin/blacklist/custom")
async def get_custom_blacklist(auth: dict = Depends(verify_token("admin"))):
    """获取自定义黑名单列表"""
    try:
        custom_ips = ip_blacklist_service.get_custom_blacklist()
        summary = ip_blacklist_service.get_blacklist_summary()

        return {
            "success": True,
            "custom_ips": custom_ips,
            "summary": summary,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取自定义黑名单失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get custom blacklist: {str(e)}")

@app.post("/api/admin/blacklist/custom/add")
async def add_ip_to_custom_blacklist(
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """添加IP到自定义黑名单"""
    ip = request.get("ip")
    reason = request.get("reason", "手动添加")

    if not ip:
        raise HTTPException(status_code=400, detail="IP address is required")

    try:
        success = ip_blacklist_service.add_custom_ip(ip, reason)
        if success:
            logger.info(f"管理员手动添加IP到黑名单: {ip} (原因: {reason})")
            return {
                "success": True,
                "message": f"IP {ip} 已添加到自定义黑名单",
                "ip": ip,
                "reason": reason,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="无效的IP地址格式")
    except Exception as e:
        logger.error(f"添加IP到自定义黑名单失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to add IP to custom blacklist: {str(e)}")

@app.post("/api/admin/blacklist/custom/remove")
async def remove_ip_from_custom_blacklist(
    request: dict,
    auth: dict = Depends(verify_token("admin"))
):
    """从自定义黑名单中移除IP"""
    ip = request.get("ip")

    if not ip:
        raise HTTPException(status_code=400, detail="IP address is required")

    try:
        success = ip_blacklist_service.remove_custom_ip(ip)
        if success:
            logger.info(f"管理员从自定义黑名单中移除IP: {ip}")
            return {
                "success": True,
                "message": f"IP {ip} 已从自定义黑名单中移除",
                "ip": ip,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "message": f"IP {ip} 不在自定义黑名单中",
                "ip": ip,
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        logger.error(f"从自定义黑名单移除IP失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to remove IP from custom blacklist: {str(e)}")

# ==================== 表单管理API ====================

@app.get("/api/admin/form-submissions", response_model=List[FormSubmissionResponse])
async def get_form_submissions(
    status: Optional[int] = None,
    limit: int = 50,
    offset: int = 0,
    auth: dict = Depends(verify_token("admin"))
):
    """获取表单提交列表"""
    try:

        async with db_service.async_session() as session:
            from sqlalchemy import select, desc

            # 构建查询
            query = select(FormSubmission).order_by(desc(FormSubmission.submitted_at))

            # 按状态过滤
            if status is not None:
                query = query.where(FormSubmission.status == status)

            # 分页
            query = query.offset(offset).limit(limit)

            result = await session.execute(query)
            submissions = result.scalars().all()

            # 转换为响应模型
            return [
                FormSubmissionResponse(
                    id=sub.id,
                    session_id=sub.session_id,
                    category=sub.category,
                    form_data=sub.form_data,
                    status=sub.status,
                    priority=sub.priority,
                    customer_email=sub.customer_email,
                    customer_order=sub.customer_order,
                    submitted_at=sub.submitted_at,
                    processed_at=sub.processed_at,
                    processed_by=sub.processed_by,
                    notes=sub.notes
                ) for sub in submissions
            ]

    except Exception as e:
        logger.error(f"获取表单提交列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get form submissions: {str(e)}")

@app.put("/api/admin/form-submissions/{submission_id}")
async def update_form_submission(
    submission_id: int,
    update_data: FormSubmissionUpdateRequest,
    auth: dict = Depends(verify_token("admin"))
):
    """更新表单提交状态"""
    try:

        async with db_service.async_session() as session:
            from sqlalchemy import select, update

            # 查找表单提交
            result = await session.execute(
                select(FormSubmission).where(FormSubmission.id == submission_id)
            )
            submission = result.scalar_one_or_none()

            if not submission:
                raise HTTPException(status_code=404, detail="Form submission not found")

            # 准备更新数据
            update_values = {}
            if update_data.status is not None:
                update_values['status'] = update_data.status
                if update_data.status == 1:  # 标记为已处理
                    update_values['processed_at'] = datetime.now()

            if update_data.notes is not None:
                update_values['notes'] = update_data.notes

            if update_data.processed_by is not None:
                update_values['processed_by'] = update_data.processed_by

            # 执行更新
            if update_values:
                await session.execute(
                    update(FormSubmission)
                    .where(FormSubmission.id == submission_id)
                    .values(**update_values)
                )
                await session.commit()

            logger.info(f"表单提交已更新: ID={submission_id}, 更新字段={list(update_values.keys())}")

            return {
                "success": True,
                "message": "Form submission updated successfully",
                "submission_id": submission_id,
                "updated_fields": list(update_values.keys())
            }

    except Exception as e:
        logger.error(f"更新表单提交失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update form submission: {str(e)}")

if __name__ == "__main__":
    from utils.path_utils import get_ssl_cert_path, get_ssl_key_path

    # 获取基本配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    https_mode = os.getenv("HTTPS_MODE", "disabled").lower()

    # 设置日志过滤器
    setup_logging_filters()

    # 基础uvicorn配置
    uvicorn_config = {
        "app": "main:app",
        "host": host,
        "port": port,
        "reload": False,  # 生产环境禁用热重载
        "access_log": False,  # 禁用访问日志
        "log_level": "warning",  # 减少无关日志输出
        "server_header": False,  # 隐藏服务器信息
        "date_header": False,   # 减少响应头
        "timeout_keep_alive": 5,  # 保持连接超时时间
        "timeout_graceful_shutdown": 10,  # 优雅关闭超时
        "limit_concurrency": 1000,  # 并发连接限制
        "limit_max_requests": 10000,  # 最大请求数限制
        "backlog": 2048,  # 连接队列大小
        "log_config": configure_uvicorn_logging()  # 使用自定义日志配置
    }

    # SSL/HTTPS配置
    if https_mode == "direct":
        logger.startup("检测HTTPS模式: direct - 配置SSL证书...")

        cert_path = get_ssl_cert_path()
        key_path = get_ssl_key_path()

        if cert_path and key_path:
            uvicorn_config["ssl_certfile"] = str(cert_path)
            uvicorn_config["ssl_keyfile"] = str(key_path)
            logger.success(f"SSL证书配置成功 - 证书: {cert_path}, 私钥: {key_path}")
            logger.startup(f"🔒 HTTPS服务将启动在 https://{host}:{port}")
        else:
            logger.error("SSL证书文件未找到，将使用HTTP模式启动")
            logger.warning("请检查ssl/目录中的证书文件或修改.env中的SSL配置")
            logger.startup(f"🌐 HTTP服务将启动在 http://{host}:{port}")
    elif https_mode == "cloudflare":
        logger.startup("检测HTTPS模式: cloudflare - 后端使用HTTP，HTTPS由Cloudflare处理")
        logger.startup(f"🌐 HTTP服务将启动在 http://{host}:{port} (Cloudflare Tunnel模式)")
    else:
        logger.startup("检测HTTPS模式: disabled - 使用HTTP模式")
        logger.startup(f"🌐 HTTP服务将启动在 http://{host}:{port}")

    # 启动服务器
    uvicorn.run(**uvicorn_config)
