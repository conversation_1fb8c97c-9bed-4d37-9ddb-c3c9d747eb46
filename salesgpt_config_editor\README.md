# 杨杋AI Agent配置管理器

一个专业的可视化配置编辑工具，用于管理杨杋AI Agent的配置文件。

## 🎯 功能特点

- **🎨 可视化编辑**: 图形界面编辑配置，告别手动编辑 JSON 文件
- **📁 文件管理**: 支持配置文件选择、历史记录、自动检测
- **✅ 实时验证**: 配置格式验证，避免错误配置
- **📋 模板支持**: 内置产品模板，快速添加产品信息
- **🔄 备份恢复**: 自动备份配置文件，支持版本管理
- **🌏 中文界面**: 完全中文化界面，详细的配置说明

## 📦 安装要求

- Python 3.8 或更高版本
- PyQt6 库

## 🚀 快速开始

### 方法1：使用启动脚本（推荐）

1. 双击运行 `run_config_editor.bat`
2. 脚本会自动检查环境并安装依赖
3. 启动配置管理器

### 方法2：手动安装

1. 安装依赖包：
```bash
pip install -r requirements.txt
```

2. 运行程序：
```bash
python main.py
```

## 📖 使用指南

### 1. 加载配置文件

1. 启动程序后，点击左侧导航的 "📁 文件管理"
2. 选择配置文件：
   - **配置文件**: 选择 `advanced_sales_config.json`
   - **产品目录**: 选择 `product_catalog.txt`
3. 点击 "📂 加载配置" 或使用 "🔍 自动检测"

### 2. 编辑配置

#### Agent 配置
- **Agent名称**: AI代理的显示名称
- **Agent角色**: 职位角色，影响对话风格
- **公司信息**: 公司名称、网站、业务描述
- **个性化设置**: 对话风格、语调、工作方法
- **专业领域**: AI的专业技能领域

#### 产品目录
- 使用内置编辑器编辑产品信息
- 支持产品模板快速添加
- 实时格式验证和字符统计
- 支持格式化和示例加载

### 3. 保存配置

- 使用 `Ctrl+S` 或点击 "💾 保存" 按钮
- 程序会自动创建备份文件
- 保存前会进行配置验证

### 4. 验证配置

- 使用 `F5` 或点击 "✅ 验证" 按钮
- 检查配置文件格式和必需字段
- 显示详细的验证结果

## 🎨 界面说明

### 主界面布局

```
┌─────────────────────────────────────────┐
│ 菜单栏: 文件 编辑 帮助                   │
├─────────────────────────────────────────┤
│ 工具栏: [保存] [验证] [格式化] [状态]    │
├─────────────────────────────────────────┤
│ 左侧导航树        │ 右侧配置面板        │
│ ├─ 📁 文件管理    │ ┌─────────────────┐ │
│ ├─ 🤖 Agent配置   │ │                 │ │
│ ├─ 📦 产品目录    │ │   配置编辑区域   │ │
│ ├─ 🎯 销售流程    │ │                 │ │
│ ├─ 🛒 购买渠道    │ │                 │ │
│ ├─ 🔧 售后支持    │ └─────────────────┘ │
│ └─ ⚙️ 高级设置    │                     │
├─────────────────────────────────────────┤
│ 状态栏: 就绪                            │
└─────────────────────────────────────────┘
```

### 配置面板

1. **📁 文件管理**: 选择和管理配置文件
2. **🤖 Agent配置**: 编辑AI代理基本信息
3. **📦 产品目录**: 编辑产品信息和目录
4. **🎯 销售流程**: 配置销售阶段和策略（开发中）
5. **🛒 购买渠道**: 设置购买链接和渠道（开发中）
6. **🔧 售后支持**: 配置售后服务（开发中）
7. **⚙️ 高级设置**: 变量管理等高级功能（开发中）

## 🔧 配置文件说明

### advanced_sales_config.json
包含 SalesGPT 的完整配置：
- Agent 基本信息
- 销售流程设置
- 购买渠道配置
- 售后支持设置
- 个性化配置

### 统一配置文件
注意：产品信息现已整合到 `advanced_sales_config.json` 中，不再需要单独的 `product_catalog.txt` 文件。

产品信息现在包含在配置文件的 `products` 部分：
```json
{
  "products": {
    "PRODUCT_ID": {
      "basic_info": { "name": "产品名称", "price": "价格" },
      "technical_specs": { "coverage_area": "适用面积" },
      "purchase_channels": { "amazon": {...}, "official_website": {...} },
      "competitor_comparison": [...],
      "certifications": [...],
      "customer_data": {...}
    }
  }
}
```

## 🛡️ 安全特性

- **自动备份**: 每次保存前自动创建备份文件
- **格式验证**: 保存前验证配置格式
- **错误处理**: 完善的错误提示和恢复机制
- **版本控制**: 支持配置文件版本管理

## 📝 快捷键

- `Ctrl+N`: 新建配置
- `Ctrl+O`: 打开配置
- `Ctrl+S`: 保存配置
- `Ctrl+Shift+S`: 另存为
- `F5`: 验证配置
- `Ctrl+Q`: 退出程序

## 🐛 故障排除

### 常见问题

1. **程序无法启动**
   - 检查 Python 版本（需要 3.8+）
   - 安装 PyQt6：`pip install PyQt6`

2. **配置文件加载失败**
   - 检查文件路径是否正确
   - 确认文件格式为 UTF-8 编码
   - 验证 JSON 文件格式

3. **保存失败**
   - 检查文件写入权限
   - 确认磁盘空间充足
   - 查看错误提示信息

### 日志文件

程序运行日志保存在：
- Windows: `config_editor_settings.json`
- 包含最近使用的配置文件历史

## 🔄 版本历史

### v1.0.0 (当前版本)
- ✅ 基础文件管理功能
- ✅ Agent 配置编辑
- ✅ 产品目录编辑器
- ✅ 配置验证和保存
- ✅ 中文界面支持

### 计划功能
- 🔄 销售流程可视化编辑
- 🔄 购买渠道管理
- 🔄 售后支持配置
- 🔄 变量管理器
- 🔄 配置模板和向导
- 🔄 在线帮助系统

## 📞 技术支持

如果遇到问题或需要帮助：

1. 查看本 README 文档
2. 检查配置文件格式
3. 查看程序错误提示
4. 联系技术支持团队

## 📄 许可证

本项目为内部工具，仅供 SalesGPT 项目使用。

---

**SalesGPT 配置管理器 v1.0**  
让配置管理变得简单高效！ 🚀
