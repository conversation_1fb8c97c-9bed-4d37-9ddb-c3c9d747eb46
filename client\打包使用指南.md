# YF AI Chat 客服端打包指南

## 概述

本指南将帮助您将YF AI Chat客服端打包成独立的exe文件，实现以下功能：
- ✅ exe所在目录读取.env文件
- ✅ 无论exe在哪都会寻找exe旁边的文件
- ✅ 如果没有就创建在旁边
- ✅ 有的话就直接读取.env、mail.json、users.json

## 快速开始

### 方法1：使用批处理文件（推荐）

1. 双击运行 `打包客服端.bat`
2. 等待打包完成
3. 在 `dist` 目录中找到 `YF_AI_Chat_Client.exe`

### 方法2：手动打包

1. 安装PyInstaller：
   ```bash
   pip install pyinstaller
   ```

2. 运行打包脚本：
   ```bash
   python build.py
   ```

## 打包后的文件结构

```
dist/
├── YF_AI_Chat_Client.exe    # 主程序
├── .env                     # 主配置文件
├── mail.json               # 邮箱配置
├── users.json              # 用户配置
└── README.txt              # 使用说明
```

## 配置文件说明

### .env 文件
主要配置文件，包含服务器连接信息：

```env
# 服务器地址
SERVER_HOST=localhost
SERVER_PORT=8000

# 管理员Token（需要从后端获取）
ADMIN_TOKEN=your_admin_token_here

# SSL配置
USE_HTTPS=false

# 时区配置
CLIENT_TIMEZONE=America/Los_Angeles
```

### mail.json 文件
邮箱配置文件，程序会自动创建：

```json
{
  "mailboxes": {},
  "version": "1.0",
  "last_updated": "2024-01-01T00:00:00"
}
```

### users.json 文件
用户账户配置，包含默认admin用户：

```json
{
  "users": {
    "admin": {
      "password_hash": "...",
      "role": "admin",
      "name": "系统管理员",
      "active": true
    }
  },
  "version": "1.0"
}
```

## 部署和使用

### 1. 复制到目标机器

将整个 `dist` 目录复制到目标机器的任意位置。

### 2. 配置服务器连接

编辑 `.env` 文件，设置正确的服务器信息：

```env
# 本地开发
SERVER_HOST=localhost
USE_HTTPS=false

# 局域网连接
SERVER_HOST=*************
USE_HTTPS=false

# 远程连接
SERVER_HOST=chat.22668.xyz
USE_HTTPS=true
```

### 3. 运行程序

双击 `YF_AI_Chat_Client.exe` 启动程序。

## 技术特性

### 智能路径管理

程序使用了智能路径管理系统：

- **开发环境**：从client目录读取配置文件
- **打包环境**：从exe所在目录读取配置文件
- **自动创建**：如果配置文件不存在，自动创建默认配置

### 配置文件自动管理

- 程序启动时自动检查配置文件
- 缺失的配置文件会自动创建
- 保持向后兼容性

### 错误处理

- 配置文件读取失败时的降级处理
- 详细的错误日志记录
- 用户友好的错误提示

## 故障排除

### 常见问题

1. **无法连接服务器**
   - 检查 `.env` 文件中的 `SERVER_HOST` 和 `SERVER_PORT`
   - 确认 `ADMIN_TOKEN` 与后端配置一致
   - 检查网络连接

2. **配置文件不生效**
   - 确认配置文件在exe同目录
   - 检查文件编码是否为UTF-8
   - 重启程序

3. **程序无法启动**
   - 检查是否缺少必要的DLL文件
   - 确认目标机器的Windows版本兼容性
   - 查看错误日志

### 调试模式

如需调试，可以运行测试脚本：

```bash
python test_config.py
```

这将显示详细的配置文件读取信息。

## 更新和维护

### 重新打包

当代码有更新时，重新运行打包脚本：

```bash
python build.py
```

### 配置迁移

新版本会自动处理配置文件的向后兼容性。

## 技术支持

如有问题，请联系技术支持团队，并提供：
- 错误截图
- 配置文件内容
- 系统环境信息
